from django.contrib import admin
from django.utils.html import format_html
from .models import Notification, EmailTemplate, EmailLog


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Admin interface for Notifications
    """
    list_display = [
        'title', 'recipient', 'type', 'priority',
        'is_read', 'email_sent', 'created_at'
    ]
    list_filter = ['type', 'priority', 'is_read', 'email_sent', 'created_at']
    search_fields = ['title', 'recipient__username', 'recipient__employee_id']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at', 'read_at', 'email_sent_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('recipient', 'type', 'priority', 'title', 'message')
        }),
        ('Related Objects', {
            'fields': ('related_request',)
        }),
        ('Status', {
            'fields': ('is_read', 'read_at', 'email_sent', 'email_sent_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        """Mark selected notifications as read"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = 'Mark selected notifications as read'

    def mark_as_unread(self, request, queryset):
        """Mark selected notifications as unread"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notifications marked as unread.')
    mark_as_unread.short_description = 'Mark selected notifications as unread'


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    """
    Admin interface for Email Templates
    """
    list_display = ['name', 'subject', 'is_active', 'created_by', 'updated_at']
    list_filter = ['is_active', 'created_at', 'updated_at']
    search_fields = ['name', 'subject']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'subject', 'is_active')
        }),
        ('Content', {
            'fields': ('body_text', 'body_html')
        }),
        ('Documentation', {
            'fields': ('available_variables',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )


@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    """
    Admin interface for Email Logs
    """
    list_display = [
        'subject', 'recipient_email', 'status',
        'attempts', 'created_at', 'sent_at'
    ]
    list_filter = ['status', 'created_at', 'sent_at']
    search_fields = ['subject', 'recipient_email', 'recipient_user__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'sent_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('recipient_email', 'recipient_user', 'subject')
        }),
        ('Content', {
            'fields': ('body', 'template_used')
        }),
        ('Related Objects', {
            'fields': ('related_notification', 'related_request')
        }),
        ('Status', {
            'fields': ('status', 'attempts', 'error_message')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'sent_at')
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        """Make most fields readonly for existing objects"""
        if obj:  # Editing an existing object
            return self.readonly_fields + [
                'recipient_email', 'recipient_user', 'subject', 'body',
                'template_used', 'related_notification', 'related_request'
            ]
        return self.readonly_fields
