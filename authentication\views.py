from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from users.models import User
import json


@csrf_exempt
@require_http_methods(["POST"])
def login_view(request):
    """
    Handle user login
    """
    try:
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return JsonResponse({
                'error': 'Username and password are required'
            }, status=400)

        user = authenticate(request, username=username, password=password)

        if user is not None:
            if user.is_active:
                login(request, user)

                # Get or create token for API access
                token, created = Token.objects.get_or_create(user=user)

                return JsonResponse({
                    'success': True,
                    'message': 'Login successful',
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'employee_id': user.employee_id,
                        'role': user.role,
                        'department': user.department,
                        'full_name': user.get_full_name(),
                        'email': user.email,
                    },
                    'token': token.key
                })
            else:
                return JsonResponse({
                    'error': 'Account is disabled'
                }, status=403)
        else:
            return JsonResponse({
                'error': 'Invalid username or password'
            }, status=401)

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': 'An error occurred during login'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def logout_view(request):
    """
    Handle user logout
    """
    try:
        # Delete the user's token
        if request.user.is_authenticated:
            try:
                token = Token.objects.get(user=request.user)
                token.delete()
            except Token.DoesNotExist:
                pass

        logout(request)

        return JsonResponse({
            'success': True,
            'message': 'Logout successful'
        })

    except Exception as e:
        return JsonResponse({
            'error': 'An error occurred during logout'
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    Get current user profile information
    """
    user = request.user

    return Response({
        'user': {
            'id': user.id,
            'username': user.username,
            'employee_id': user.employee_id,
            'role': user.role,
            'department': user.department,
            'full_name': user.get_full_name(),
            'email': user.email,
            'phone_number': user.phone_number,
            'hire_date': user.hire_date,
            'annual_medical_pool': float(user.annual_medical_pool),
            'used_medical_pool': float(user.used_medical_pool),
            'remaining_medical_pool': float(user.remaining_medical_pool),
            'pool_usage_percentage': user.pool_usage_percentage,
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password(request):
    """
    Change user password
    """
    try:
        data = request.data
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')

        if not all([current_password, new_password, confirm_password]):
            return Response({
                'error': 'All password fields are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if new_password != confirm_password:
            return Response({
                'error': 'New passwords do not match'
            }, status=status.HTTP_400_BAD_REQUEST)

        user = request.user

        if not user.check_password(current_password):
            return Response({
                'error': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        user.set_password(new_password)
        user.save()

        # Delete all existing tokens to force re-login
        Token.objects.filter(user=user).delete()

        return Response({
            'success': True,
            'message': 'Password changed successfully. Please login again.'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while changing password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
