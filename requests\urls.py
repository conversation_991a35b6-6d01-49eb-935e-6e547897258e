from django.urls import path
from . import views

app_name = 'requests'

urlpatterns = [
    # Request management
    path('', views.request_list, name='request_list'),
    path('create/', views.create_request, name='create_request'),
    path('<uuid:request_id>/', views.request_detail, name='request_detail'),
    path('<uuid:request_id>/update/', views.update_request, name='update_request'),
    path('<uuid:request_id>/submit/', views.submit_request, name='submit_request'),

    # Request items management
    path('<uuid:request_id>/items/', views.add_request_item, name='add_request_item'),
    path('<uuid:request_id>/items/<int:item_id>/', views.update_request_item, name='update_request_item'),
    path('<uuid:request_id>/items/<int:item_id>/delete/', views.delete_request_item, name='delete_request_item'),

    # Attachments management
    path('<uuid:request_id>/attachments/', views.upload_attachment, name='upload_attachment'),
    path('<uuid:request_id>/attachments/<int:attachment_id>/delete/', views.delete_attachment, name='delete_attachment'),

    # Coversheet generation
    path('<uuid:request_id>/coversheet/', views.generate_coversheet, name='generate_coversheet'),
]
