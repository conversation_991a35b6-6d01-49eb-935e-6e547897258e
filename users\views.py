from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
import logging
from .models import User, Dependent
from requests.models import MedicalRequest, RequestDeadline
from notifications.models import Notification

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def employee_dashboard(request):
    """
    Employee dashboard with overview information
    """
    user = request.user

    # Get current month deadline
    current_month = timezone.now().replace(day=1)
    try:
        deadline = RequestDeadline.objects.get(month=current_month, is_active=True)
        submission_deadline = deadline.submission_deadline
    except RequestDeadline.DoesNotExist:
        submission_deadline = None

    # Get request statistics
    total_requests = MedicalRequest.objects.filter(employee=user).count()
    pending_requests = MedicalRequest.objects.filter(
        employee=user,
        status__in=['draft', 'submitted', 'under_review']
    ).count()
    approved_requests = MedicalRequest.objects.filter(
        employee=user,
        status='approved'
    ).count()

    # Get recent requests (last 5)
    recent_requests = MedicalRequest.objects.filter(employee=user).order_by('-created_at')[:5]
    recent_requests_data = []
    for req in recent_requests:
        recent_requests_data.append({
            'id': str(req.id),
            'request_number': req.request_number,
            'month': req.month.strftime('%B %Y'),
            'status': req.status,
            'total_amount': float(req.total_amount),
            'created_at': req.created_at,
            'submitted_at': req.submitted_at,
        })

    # Get unread notifications
    unread_notifications = Notification.objects.filter(
        recipient=user,
        is_read=False
    ).count()

    # Get dependents
    dependents = Dependent.objects.filter(employee=user, is_active=True)
    dependents_data = []
    for dep in dependents:
        try:
            dependents_data.append({
                'id': dep.id,
                'name': dep.name,
                'relationship': dep.relationship,
                'annual_medical_pool': float(dep.annual_medical_pool or 0),
                'used_medical_pool': float(dep.used_medical_pool or 0),
                'remaining_medical_pool': float(dep.remaining_medical_pool or 0),
                'pool_usage_percentage': dep.pool_usage_percentage or 0,
            })
        except (AttributeError, ValueError) as e:
            # Log error but continue processing other dependents
            logger.error(f"Error processing dependent {dep.id}: {e}")
            continue

    # Calculate days until deadline
    days_until_deadline = None
    if submission_deadline:
        try:
            days_until_deadline = (submission_deadline.date() - timezone.now().date()).days
        except (AttributeError, ValueError) as e:
            logger.error(f"Error calculating days until deadline: {e}")
            days_until_deadline = None

    return Response({
        'user_info': {
            'employee_id': user.employee_id or '',
            'full_name': user.get_full_name() or '',
            'department': user.department or '',
            'position': user.position or '',
            'annual_medical_pool': float(user.annual_medical_pool or 0),
            'used_medical_pool': float(user.used_medical_pool or 0),
            'remaining_medical_pool': float(user.remaining_medical_pool or 0),
            'pool_usage_percentage': user.pool_usage_percentage or 0,
            'photo_url': user.get_photo_url(),
            'initials': user.get_initials(),
        },
        'statistics': {
            'total_requests': total_requests,
            'pending_requests': pending_requests,
            'approved_requests': approved_requests,
            'unread_notifications': unread_notifications,
        },
        'deadline_info': {
            'submission_deadline': submission_deadline,
            'days_until_deadline': days_until_deadline,
        },
        'recent_requests': recent_requests_data,
        'dependents': dependents_data,
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_list(request):
    """
    List users (admin only)
    """
    # Check if user has admin role with proper error handling
    if not hasattr(request.user, 'role') or request.user.role != 'admin':
        return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)

    users = User.objects.all().order_by('employee_id')
    users_data = []
    for user in users:
        try:
            users_data.append({
                'id': user.id,
                'username': user.username or '',
                'employee_id': user.employee_id or '',
                'full_name': user.get_full_name() or '',
                'role': getattr(user, 'role', 'employee'),
                'department': user.department or '',
                'is_active': user.is_active,
                'annual_medical_pool': float(user.annual_medical_pool or 0),
                'used_medical_pool': float(user.used_medical_pool or 0),
            })
        except (AttributeError, ValueError) as e:
            # Log error but continue processing other users
            logger.error(f"Error processing user {user.id}: {e}")
            continue

    return Response({'users': users_data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dependent_list(request):
    """
    List user's dependents
    """
    dependents = Dependent.objects.filter(employee=request.user, is_active=True)
    dependents_data = []
    for dep in dependents:
        try:
            dependents_data.append({
                'id': dep.id,
                'name': dep.name or '',
                'relationship': dep.relationship or '',
                'date_of_birth': dep.date_of_birth,
                'annual_medical_pool': float(dep.annual_medical_pool or 0),
                'used_medical_pool': float(dep.used_medical_pool or 0),
                'remaining_medical_pool': float(dep.remaining_medical_pool or 0),
                'pool_usage_percentage': dep.pool_usage_percentage or 0,
            })
        except (AttributeError, ValueError) as e:
            # Log error but continue processing other dependents
            logger.error(f"Error processing dependent {dep.id}: {e}")
            continue

    return Response({'dependents': dependents_data})
