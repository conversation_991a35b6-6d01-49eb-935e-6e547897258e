from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import User, Dependent


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User Admin with additional fields
    """
    list_display = [
        'username', 'employee_id', 'get_full_name', 'role',
        'department', 'is_active_employee', 'pool_status', 'has_photo'
    ]
    list_filter = ['role', 'department', 'is_active_employee', 'is_staff', 'is_active']
    search_fields = ['username', 'employee_id', 'first_name', 'last_name', 'email']
    ordering = ['employee_id']
    readonly_fields = ['related_requests']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Employee Information', {
            'fields': ('employee_id', 'role', 'department', 'position', 'phone_number', 'photo', 'hire_date', 'is_active_employee')
        }),
        ('Medical Pool', {
            'fields': ('annual_medical_pool', 'used_medical_pool')
        }),
        ('Medical Requests History', {
            'fields': ('related_requests',),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Employee Information', {
            'fields': ('employee_id', 'role', 'department', 'phone_number', 'hire_date')
        }),
    )

    def pool_status(self, obj):
        """Display pool usage status with color coding"""
        percentage = obj.pool_usage_percentage
        remaining = obj.remaining_medical_pool

        if percentage >= 90:
            color = 'red'
        elif percentage >= 70:
            color = 'orange'
        else:
            color = 'green'

        return format_html(
            '<span style="color: {};">{} EGP ({}%)</span>',
            color, "{:.2f}".format(remaining), "{:.1f}".format(percentage)
        )
    pool_status.short_description = 'Pool Status'

    def has_photo(self, obj):
        """Check if user has uploaded a photo"""
        if obj.photo:
            return format_html('<span style="color: green;">✓ Yes</span>')
        else:
            return format_html('<span style="color: red;">✗ No</span>')
    has_photo.short_description = 'Has Photo'

    def related_requests(self, obj):
        """Show related medical requests with costs for this employee only"""
        from requests.models import MedicalRequest, RequestItem

        requests = MedicalRequest.objects.filter(employee=obj).order_by('-created_at')[:5]
        if not requests:
            return "No requests found"

        html = '<div style="max-width: 400px;">'
        total_cost = 0

        for request in requests:
            # Calculate cost for EMPLOYEE ONLY (exclude dependent items)
            employee_items = RequestItem.objects.filter(
                request=request,
                dependent__isnull=True  # Only items where dependent is null (employee items)
            )
            employee_cost = sum(item.cost for item in employee_items)
            employee_item_count = employee_items.count()
            total_cost += employee_cost

            # Create link to request detail
            request_url = reverse('admin:requests_medicalrequest_change', args=[request.id])

            # Status color coding
            status_color = {
                'draft': '#6c757d',
                'submitted': '#ffc107',
                'approved': '#28a745',
                'rejected': '#dc3545'
            }.get(request.status, '#6c757d')

            # Build the cost display - employee only
            cost_display = f'Employee: {employee_cost:.2f} EGP ({employee_item_count} items)'

            html += f'''
            <div style="border: 1px solid #ddd; margin: 2px 0; padding: 5px; border-radius: 3px;">
                <strong><a href="{request_url}" target="_blank">{request.month}</a></strong>
                <span style="color: {status_color}; font-weight: bold;">({request.get_status_display()})</span><br>
                <small>{cost_display}</small>
            </div>
            '''

        html += f'<div style="margin-top: 8px; font-weight: bold; color: #007bff;">Total Employee Cost: {total_cost:.2f} EGP</div>'
        html += '</div>'

        return mark_safe(html)

    related_requests.short_description = 'Employee Medical Requests (Employee Expenses Only)'


@admin.register(Dependent)
class DependentAdmin(admin.ModelAdmin):
    """
    Admin interface for Dependents
    """
    list_display = [
        'name', 'relationship', 'employee', 'date_of_birth',
        'is_active', 'pool_status'
    ]
    list_filter = ['relationship', 'is_active', 'employee__department']
    search_fields = ['name', 'employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering = ['employee__employee_id', 'relationship']
    readonly_fields = ['related_requests']

    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'name', 'relationship', 'date_of_birth', 'national_id')
        }),
        ('Medical Pool', {
            'fields': ('annual_medical_pool', 'used_medical_pool')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Medical Requests History', {
            'fields': ('related_requests',),
            'classes': ('collapse',)
        }),
    )

    def pool_status(self, obj):
        """Display pool usage status with color coding"""
        percentage = obj.pool_usage_percentage
        remaining = obj.remaining_medical_pool

        if percentage >= 90:
            color = 'red'
        elif percentage >= 70:
            color = 'orange'
        else:
            color = 'green'

        return format_html(
            '<span style="color: {};">{} EGP ({}%)</span>',
            color, "{:.2f}".format(remaining), "{:.1f}".format(percentage)
        )
    pool_status.short_description = 'Pool Status'

    def related_requests(self, obj):
        """Show related medical requests with costs for this dependent"""
        from requests.models import RequestItem

        # Get all request items for this dependent
        items = RequestItem.objects.filter(dependent=obj).select_related('request').order_by('-request__created_at')[:10]
        if not items:
            return "No requests found"

        html = '<div style="max-width: 400px;">'
        total_cost = 0

        # Group items by request
        requests_dict = {}
        for item in items:
            request = item.request
            if request.id not in requests_dict:
                requests_dict[request.id] = {
                    'request': request,
                    'items': [],
                    'total_cost': 0
                }
            requests_dict[request.id]['items'].append(item)
            requests_dict[request.id]['total_cost'] += item.cost
            total_cost += item.cost

        for request_data in requests_dict.values():
            request = request_data['request']
            request_cost = request_data['total_cost']
            item_count = len(request_data['items'])

            # Create link to request detail
            request_url = reverse('admin:requests_medicalrequest_change', args=[request.id])

            # Status color coding
            status_color = {
                'draft': '#6c757d',
                'submitted': '#ffc107',
                'approved': '#28a745',
                'rejected': '#dc3545'
            }.get(request.status, '#6c757d')

            html += f'''
            <div style="border: 1px solid #ddd; margin: 2px 0; padding: 5px; border-radius: 3px;">
                <strong><a href="{request_url}" target="_blank">{request.month}</a></strong>
                <span style="color: {status_color}; font-weight: bold;">({request.get_status_display()})</span><br>
                <small>Dependent: {request_cost:.2f} EGP ({item_count} items)</small>
            </div>
            '''

        html += f'<div style="margin-top: 8px; font-weight: bold; color: #007bff;">Total Dependent Cost: {total_cost:.2f} EGP</div>'
        html += '</div>'

        return mark_safe(html)

    related_requests.short_description = 'Medical Requests for this Dependent'
