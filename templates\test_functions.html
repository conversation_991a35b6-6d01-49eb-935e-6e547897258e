<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Functions</title>
</head>
<body>
    <h1>Function Test Page</h1>
    
    <div style="margin: 20px;">
        <button onclick="testAddItem()">Test Add Item Function</button>
        <button onclick="testSaveDraft()">Test Save Draft Function</button>
        <button onclick="testDragDrop()">Test Drag Drop Setup</button>
    </div>
    
    <div id="results" style="margin: 20px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
        <h3>Test Results:</h3>
        <div id="output"></div>
    </div>

    <script>
        // Mock global variables for testing
        let authToken = localStorage.getItem('authToken') || 'test-token';
        let currentUser = { full_name: 'Test User' };
        let dependents = [
            { id: 1, name: 'Test Dependent', relationship: 'wife' }
        ];
        let currentRequest = null;

        function log(message) {
            const output = document.getElementById('output');
            output.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function testAddItem() {
            log('Testing addItem function...');
            try {
                if (typeof window.addItem === 'function') {
                    log('✅ addItem function exists');
                    // Test calling it (won't actually add since containers don't exist)
                    log('Function signature: ' + window.addItem.toString().substring(0, 100) + '...');
                } else {
                    log('❌ addItem function not found');
                }
            } catch (error) {
                log('❌ Error testing addItem: ' + error.message);
            }
        }

        function testSaveDraft() {
            log('Testing saveDraft function...');
            try {
                if (typeof window.saveDraft === 'function') {
                    log('✅ saveDraft function exists');
                    log('Function signature: ' + window.saveDraft.toString().substring(0, 100) + '...');
                } else {
                    log('❌ saveDraft function not found');
                }
            } catch (error) {
                log('❌ Error testing saveDraft: ' + error.message);
            }
        }

        function testDragDrop() {
            log('Testing drag and drop setup...');
            try {
                const uploadArea = document.querySelector('.file-upload-area');
                if (uploadArea) {
                    log('✅ Upload area found');
                } else {
                    log('❌ Upload area not found');
                }
                
                // Test if event handlers are attached
                log('Checking for drag event handlers...');
                
            } catch (error) {
                log('❌ Error testing drag drop: ' + error.message);
            }
        }

        // Load the actual request form functions
        function loadRequestFormFunctions() {
            log('Loading request form functions...');
            
            // Try to load the functions from the request form
            fetch('/request-form/')
                .then(response => response.text())
                .then(html => {
                    // Extract script content
                    const scriptMatch = html.match(/<script>([\s\S]*?)<\/script>/);
                    if (scriptMatch) {
                        log('✅ Found script content in request form');
                        try {
                            // Execute the script content
                            eval(scriptMatch[1]);
                            log('✅ Script executed successfully');
                        } catch (error) {
                            log('❌ Error executing script: ' + error.message);
                        }
                    } else {
                        log('❌ No script content found');
                    }
                })
                .catch(error => {
                    log('❌ Error loading request form: ' + error.message);
                });
        }

        // Auto-load functions when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, testing functions...');
            loadRequestFormFunctions();
        });
    </script>
</body>
</html>
