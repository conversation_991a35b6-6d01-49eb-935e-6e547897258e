from django.core.management.base import BaseCommand
from users.models import User


class Command(BaseCommand):
    help = 'Setup admin user with proper employee information'

    def handle(self, *args, **options):
        try:
            admin = User.objects.get(username='admin')
            admin.employee_id = 'ADMIN001'
            admin.role = 'admin'
            admin.department = 'admin'
            admin.annual_medical_pool = 5000.00
            admin.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated admin user: {admin}')
            )
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Admin user not found. Please create superuser first.')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error updating admin user: {e}')
            )
