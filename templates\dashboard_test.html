<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Compensation Dashboard Test</title>
    <link rel="stylesheet" href="/static/css/professional.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card.warning { background: #f39c12; }
        .stat-card.success { background: #27ae60; }
        .stat-card.danger { background: #e74c3c; }
        .pool-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #bdc3c7;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }
        .progress-fill.warning { background: #f39c12; }
        .progress-fill.danger { background: #e74c3c; }
        .login-form {
            max-width: 400px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #2980b9;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        .error {
            color: #e74c3c;
            margin-top: 10px;
        }
        .hidden {
            display: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-draft { background: #f8f9fa; color: #6c757d; }
        .status-submitted { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Login Form -->
    <div id="loginForm" class="login-form">
        <h2>Medical Compensation System</h2>
        <form onsubmit="login(event)">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">Login</button>
            <div id="loginError" class="error hidden"></div>
        </form>
        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <strong>Test Accounts:</strong><br>
            Admin: admin / Admin123!<br>
            Employee: john.doe / password123<br>
            Employee: jane.smith / password123
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="hidden">
        <div class="header">
            <h1>Employee Dashboard</h1>
            <p id="welcomeMessage"></p>
            <div style="float: right;">
                <button onclick="createNewRequest()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                    + Create New Request
                </button>
                <button onclick="logout()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    Logout
                </button>
            </div>
        </div>

        <div class="main-container">
            <div id="dashboardContent"></div>
        </div>
    </div>

    <!-- Floating Notifications -->
    <div id="notificationContainer" class="notification-container"></div>

    <script>
        let authToken = null;
        let currentUser = null;

        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');
            
            try {
                const response = await fetch('/api/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    currentUser = data.user;

                    // Store token in localStorage for request form access
                    localStorage.setItem('authToken', authToken);

                    document.getElementById('loginForm').classList.add('hidden');
                    document.getElementById('dashboard').classList.remove('hidden');
                    document.getElementById('welcomeMessage').textContent =
                        `Welcome, ${currentUser.full_name} (${currentUser.employee_id})`;

                    loadDashboard();
                    showWelcomeNotification();
                } else {
                    errorDiv.textContent = data.error || 'Login failed';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = 'Network error. Please try again.';
                errorDiv.classList.remove('hidden');
            }
        }

        async function loadDashboard() {
            try {
                const response = await fetch('/api/users/dashboard/', {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    renderDashboard(data);
                } else {
                    console.error('Failed to load dashboard');
                }
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        function renderDashboard(data) {
            const content = document.getElementById('dashboardContent');
            
            const poolUsageClass = data.user_info.pool_usage_percentage >= 90 ? 'danger' : 
                                  data.user_info.pool_usage_percentage >= 70 ? 'warning' : 'success';
            
            content.innerHTML = `
                <!-- Professional Header -->
                <div class="professional-header">
                    <div class="header-content">
                        <div class="header-left">
                            <div class="user-avatar-container">
                                ${data.user_info.photo_url ?
                                    `<img src="${data.user_info.photo_url}" alt="Profile" class="user-avatar-large">` :
                                    `<div class="avatar-initials avatar-initials-large">${data.user_info.initials}</div>`
                                }
                            </div>
                            <div>
                                <h1 class="header-title">Medical Compensation Dashboard</h1>
                                <p class="header-subtitle">Manage your medical expenses and requests</p>
                            </div>
                        </div>
                        <div class="header-right">
                            <div class="user-info">
                                <p class="user-name">${data.user_info.full_name}</p>
                                <p class="user-role">${data.user_info.employee_id} • ${data.user_info.department}</p>
                                <p class="user-position">${data.user_info.position || 'Position not specified'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card-professional">
                        <div class="stat-icon stat-icon-primary">📊</div>
                        <div class="stat-count-badge stat-count-primary">${data.statistics.total_requests}</div>
                        <p class="stat-label">Total Requests</p>
                    </div>
                    <div class="stat-card-professional">
                        <div class="stat-icon stat-icon-warning">⏳</div>
                        <div class="stat-count-badge stat-count-warning">${data.statistics.pending_requests}</div>
                        <p class="stat-label">Pending Requests</p>
                    </div>
                    <div class="stat-card-professional">
                        <div class="stat-icon stat-icon-success">✅</div>
                        <div class="stat-count-badge stat-count-success">${data.statistics.approved_requests}</div>
                        <p class="stat-label">Approved Requests</p>
                    </div>

                </div>

                <!-- Medical Pool Information -->
                <div class="professional-card">
                    <div class="card-header-professional">
                        <h3>💰 Medical Pool Status</h3>
                    </div>
                    <div class="card-body-professional">
                        <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin-bottom: 20px;">
                            <div style="text-align: center;">
                                <div class="stat-value" style="font-size: 24px; color: #667eea;">${data.user_info.annual_medical_pool.toFixed(0)}</div>
                                <div class="stat-label">Annual Pool (EGP)</div>
                            </div>
                            <div style="text-align: center;">
                                <div class="stat-value" style="font-size: 24px; color: #f5576c;">${data.user_info.used_medical_pool.toFixed(0)}</div>
                                <div class="stat-label">Used (EGP)</div>
                            </div>
                            <div style="text-align: center;">
                                <div class="stat-value" style="font-size: 24px; color: #56ab2f;">${data.user_info.remaining_medical_pool.toFixed(0)}</div>
                                <div class="stat-label">Remaining (EGP)</div>
                            </div>
                        </div>
                        <div class="progress-bar" style="height: 12px; background: #f0f0f0; border-radius: 6px; overflow: hidden;">
                            <div class="progress-fill" style="height: 100%; background: linear-gradient(90deg, #56ab2f 0%, #f5576c 100%); width: ${data.user_info.pool_usage_percentage}%; transition: width 0.3s ease;"></div>
                        </div>
                        <p style="margin-top: 10px; text-align: center; font-weight: 600; color: #666;">${data.user_info.pool_usage_percentage.toFixed(1)}% of annual pool used</p>
                    </div>
                </div>

                <!-- Deadline Information -->
                <div class="professional-card">
                    <div class="card-header-professional">
                        <h3>⏰ Submission Deadline</h3>
                    </div>
                    <div class="card-body-professional">
                        ${data.deadline_info.submission_deadline ? `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="stat-value" style="font-size: 20px; color: #667eea;">${new Date(data.deadline_info.submission_deadline).toLocaleDateString()}</div>
                                    <div class="stat-label">Next Deadline</div>
                                </div>
                                <div style="text-align: right;">
                                    <div class="stat-value" style="font-size: 20px; color: ${data.deadline_info.days_until_deadline <= 7 ? '#f5576c' : '#56ab2f'};">${data.deadline_info.days_until_deadline}</div>
                                    <div class="stat-label">Days Remaining</div>
                                </div>
                            </div>
                        ` : '<p style="text-align: center; color: #666;">No upcoming deadlines</p>'}
                    </div>
                </div>

                <!-- Recent Requests -->
                <div class="professional-card">
                    <div class="card-header-professional">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h3>📋 Recent Requests</h3>
                            <button onclick="viewAllRequests()" class="btn-professional-outline">View All Requests</button>
                        </div>
                    </div>
                    <div class="card-body-professional">
                        ${data.recent_requests.length > 0 ? `
                            <table class="table-professional">
                                <thead>
                                    <tr>
                                        <th>Request #</th>
                                        <th>Month</th>
                                        <th>Status</th>
                                        <th>Amount</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.recent_requests.map(req => `
                                        <tr>
                                            <td><strong>${req.request_number}</strong></td>
                                            <td>${req.month}</td>
                                            <td><span class="status-badge status-${req.status}">${req.status}</span></td>
                                            <td><strong>${req.total_amount.toFixed(2)} EGP</strong></td>
                                            <td>${new Date(req.created_at).toLocaleDateString()}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        ` : '<p style="text-align: center; color: #666; padding: 20px;">No requests found</p>'}
                    </div>
                </div>

                <!-- Dependents -->
                <div class="professional-card">
                    <div class="card-header-professional">
                        <h3>👨‍👩‍👧‍👦 Dependents</h3>
                    </div>
                    <div class="card-body-professional">
                        ${data.dependents.length > 0 ? `
                            <table class="table-professional">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Relationship</th>
                                        <th>Pool Status</th>
                                        <th>Usage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.dependents.map(dep => {
                                        const depUsageClass = dep.pool_usage_percentage >= 90 ? 'danger' :
                                                             dep.pool_usage_percentage >= 70 ? 'warning' : 'success';
                                        return `
                                            <tr>
                                                <td><strong>${dep.name}</strong></td>
                                                <td><span class="status-badge" style="background: #e9ecef; color: #495057; border: 1px solid #dee2e6;">${dep.relationship}</span></td>
                                                <td><strong>${dep.remaining_medical_pool.toFixed(0)} / ${dep.annual_medical_pool.toFixed(0)} EGP</strong></td>
                                                <td>
                                                    <div style="display: flex; align-items: center; gap: 10px;">
                                                        <div class="progress-bar" style="width: 80px; height: 8px; background: #f0f0f0; border-radius: 4px; overflow: hidden;">
                                                            <div style="height: 100%; background: ${dep.pool_usage_percentage >= 90 ? '#f5576c' : dep.pool_usage_percentage >= 70 ? '#f093fb' : '#56ab2f'}; width: ${dep.pool_usage_percentage}%; transition: width 0.3s ease;"></div>
                                                        </div>
                                                        <span style="font-weight: 600; color: #666;">${dep.pool_usage_percentage.toFixed(1)}%</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        ` : '<p style="text-align: center; color: #666; padding: 20px;">No dependents found</p>'}
                    </div>
                </div>
            `;
        }

        function createNewRequest() {
            // Store the auth token in localStorage so the request form can access it
            localStorage.setItem('authToken', authToken);
            // Open the request form in a new tab
            window.open('/request-form/', '_blank');
        }

        function viewAllRequests() {
            // For now, just show an alert. In a full implementation, this would open a requests list page
            alert('View All Requests feature - would open a detailed requests list page');
        }

        function logout() {
            fetch('/api/auth/logout/', {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${authToken}`,
                    'Content-Type': 'application/json',
                }
            }).then(() => {
                authToken = null;
                currentUser = null;
                localStorage.removeItem('authToken'); // Clear stored token
                document.getElementById('dashboard').classList.add('hidden');
                document.getElementById('loginForm').classList.remove('hidden');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('loginError').classList.add('hidden');
            });
        }

        // Notification System
        function showNotification(title, message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <button class="notification-close" onclick="closeNotification(this)">&times;</button>
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            `;

            container.appendChild(notification);

            // Show notification with animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                closeNotification(notification.querySelector('.notification-close'));
            }, 5000);
        }

        function closeNotification(button) {
            const notification = button.parentElement;
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }

        // Show welcome notification after login
        function showWelcomeNotification() {
            if (currentUser) {
                showNotification(
                    'Welcome Back!',
                    `Hello ${currentUser.full_name}, you have successfully logged in.`,
                    'success'
                );
            }
        }
    </script>
</body>
</html>
