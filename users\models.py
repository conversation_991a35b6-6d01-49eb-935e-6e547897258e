from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
import os


def user_photo_upload_path(instance, filename):
    """Generate upload path for user photos"""
    return f'users/photos/{instance.employee_id}/{filename}'


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    """
    ROLE_CHOICES = [
        ('employee', 'Employee'),
        ('medical_assistant', 'Medical Assistant'),
        ('medical_poss', 'Medical Poss'),
        ('admin', 'System Admin'),
    ]

    DEPARTMENT_CHOICES = [
        ('hr', 'Human Resources'),
        ('finance', 'Finance'),
        ('engineering', 'Engineering'),
        ('operations', 'Operations'),
        ('medical', 'Medical Department'),
        ('admin', 'Administration'),
    ]

    # Basic Information
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^[A-Z0-9]+$', 'Employee ID must contain only uppercase letters and numbers')]
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='employee')
    department = models.CharField(max_length=20, choices=DEPARTMENT_CHOICES)
    position = models.CharField(max_length=100, blank=True, null=True, help_text="Job position/title")

    # Contact Information
    phone_number = models.CharField(
        max_length=15,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'Phone number must be valid')]
    )

    # Profile Photo
    photo = models.ImageField(
        upload_to=user_photo_upload_path,
        blank=True,
        null=True,
        help_text="Profile photo (optional)"
    )

    # Employment Information
    hire_date = models.DateField(null=True, blank=True)
    is_active_employee = models.BooleanField(default=True)

    # Medical Pool Information
    annual_medical_pool = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    used_medical_pool = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.employee_id} - {self.get_full_name()}"

    @property
    def remaining_medical_pool(self):
        """Calculate remaining medical pool balance"""
        return self.annual_medical_pool - self.used_medical_pool

    @property
    def pool_usage_percentage(self):
        """Calculate pool usage percentage"""
        if self.annual_medical_pool > 0:
            return (self.used_medical_pool / self.annual_medical_pool) * 100
        return 0

    def can_submit_request(self):
        """Check if user can submit new requests"""
        return self.is_active_employee and self.role == 'employee'

    def get_photo_url(self):
        """Get user photo URL or default avatar"""
        if self.photo and hasattr(self.photo, 'url'):
            return self.photo.url
        # Return default avatar based on role
        role_avatars = {
            'admin': '/static/images/avatars/admin-default.svg',
            'medical_assistant': '/static/images/avatars/medical-default.svg',
            'medical_poss': '/static/images/avatars/medical-default.svg',
            'employee': '/static/images/avatars/employee-default.svg',
        }
        return role_avatars.get(self.role, '/static/images/avatars/default.svg')

    def get_initials(self):
        """Get user initials for avatar fallback"""
        if self.first_name and self.last_name:
            return f"{self.first_name[0]}{self.last_name[0]}".upper()
        elif self.first_name:
            return self.first_name[0].upper()
        elif self.username:
            return self.username[0].upper()
        return "U"


class Dependent(models.Model):
    """
    Model for employee dependents
    """
    RELATIONSHIP_CHOICES = [
        ('father', 'Father'),
        ('mother', 'Mother'),
        ('wife', 'Wife'),
        ('son', 'Son'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dependents')
    name = models.CharField(max_length=100)
    relationship = models.CharField(max_length=10, choices=RELATIONSHIP_CHOICES)
    date_of_birth = models.DateField()
    national_id = models.CharField(max_length=20, unique=True, blank=True, null=True)

    # Medical Pool Information for dependents
    annual_medical_pool = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    used_medical_pool = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dependents'
        unique_together = ['employee', 'relationship']  # One relationship type per employee
        verbose_name = 'Dependent'
        verbose_name_plural = 'Dependents'

    def __str__(self):
        return f"{self.name} ({self.relationship}) - {self.employee.employee_id}"

    @property
    def remaining_medical_pool(self):
        """Calculate remaining medical pool balance"""
        return self.annual_medical_pool - self.used_medical_pool

    @property
    def pool_usage_percentage(self):
        """Calculate pool usage percentage"""
        if self.annual_medical_pool > 0:
            return (self.used_medical_pool / self.annual_medical_pool) * 100
        return 0
