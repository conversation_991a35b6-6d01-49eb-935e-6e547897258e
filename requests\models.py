from django.db import models
from django.conf import settings
from django.core.validators import FileExtensionValidator, MinValueValidator
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from users.models import Dependent
import uuid
import os


def request_file_upload_path(instance, filename):
    """Generate upload path for request attachments"""
    return f'requests/{instance.request.id}/{filename}'


class MedicalRequest(models.Model):
    """
    Main model for medical compensation requests
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('returned', 'Returned for Correction'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid'),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    employee = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='medical_requests')
    request_number = models.CharField(max_length=20, unique=True, blank=True)

    # Request Details
    month = models.DateField(help_text="Month for which the request is being made")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Totals
    employee_normal_total = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    employee_chronic_total = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    dependents_normal_total = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    dependents_chronic_total = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Comments and Notes
    employee_notes = models.TextField(blank=True, help_text="Employee's additional notes")
    reviewer_comments = models.TextField(blank=True, help_text="Medical assistant's comments")
    approver_comments = models.TextField(blank=True, help_text="Medical Poss comments")

    # Review Information
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_requests'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_requests'
    )
    approved_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    submitted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'medical_requests'
        ordering = ['-created_at']
        verbose_name = 'Medical Request'
        verbose_name_plural = 'Medical Requests'

    def __str__(self):
        return f"{self.request_number} - {self.employee.employee_id} ({self.status})"

    @property
    def total_amount(self):
        """Calculate total request amount"""
        return (self.employee_normal_total + self.employee_chronic_total +
                self.dependents_normal_total + self.dependents_chronic_total)

    @property
    def employee_total(self):
        """Calculate total employee amount"""
        return self.employee_normal_total + self.employee_chronic_total

    @property
    def dependents_total(self):
        """Calculate total dependents amount"""
        return self.dependents_normal_total + self.dependents_chronic_total

    def save(self, *args, **kwargs):
        if not self.request_number:
            # Generate request number: REQ-YYYY-MM-XXXX
            from datetime import datetime
            now = datetime.now()
            count = MedicalRequest.objects.filter(
                created_at__year=now.year,
                created_at__month=now.month
            ).count() + 1
            self.request_number = f"REQ-{now.year}-{now.month:02d}-{count:04d}"
        super().save(*args, **kwargs)


class RequestItem(models.Model):
    """
    Individual items within a medical request
    """
    CATEGORY_CHOICES = [
        ('operation', 'Operation'),
        ('glasses', 'Glasses'),
        ('dental', 'Dental'),
        ('medicine', 'Medicine'),
        ('dl_services', 'D&L Services'),
        ('other', 'Other'),
    ]

    TYPE_CHOICES = [
        ('normal', 'Normal'),
        ('chronic', 'Chronic'),
    ]

    BENEFICIARY_CHOICES = [
        ('employee', 'Employee'),
        ('dependent', 'Dependent'),
    ]

    # Relationships
    request = models.ForeignKey(MedicalRequest, on_delete=models.CASCADE, related_name='items')
    dependent = models.ForeignKey(Dependent, on_delete=models.CASCADE, null=True, blank=True)

    # Item Details
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    beneficiary = models.CharField(max_length=10, choices=BENEFICIARY_CHOICES)
    description = models.TextField()
    cost = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)])
    date_of_service = models.DateField()

    # Validation flags
    is_flagged = models.BooleanField(default=False)
    flag_reason = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'request_items'
        ordering = ['created_at']
        verbose_name = 'Request Item'
        verbose_name_plural = 'Request Items'

    def __str__(self):
        beneficiary_name = self.dependent.name if self.dependent else self.request.employee.get_full_name()
        return f"{self.category} - {beneficiary_name} - ${self.cost}"


class RequestAttachment(models.Model):
    """
    File attachments for medical requests
    """
    ATTACHMENT_TYPES = [
        ('prescription', 'Prescription'),
        ('doctor_report', 'Doctor Report'),
        ('invoice', 'Invoice'),
        ('receipt', 'Receipt'),
        ('dl_request', 'D&L Request'),
        ('other', 'Other'),
    ]

    request = models.ForeignKey(MedicalRequest, on_delete=models.CASCADE, related_name='attachments')
    item = models.ForeignKey(RequestItem, on_delete=models.CASCADE, null=True, blank=True, related_name='attachments')

    file = models.FileField(
        upload_to=request_file_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'])]
    )
    attachment_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPES)
    description = models.CharField(max_length=255, blank=True)
    file_size = models.PositiveIntegerField(help_text="File size in bytes")

    # Validation
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_attachments'
    )
    verified_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'request_attachments'
        ordering = ['created_at']
        verbose_name = 'Request Attachment'
        verbose_name_plural = 'Request Attachments'

    def __str__(self):
        return f"{self.attachment_type} - {self.request.request_number}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
        super().save(*args, **kwargs)


class RequestAuditLog(models.Model):
    """
    Audit log for tracking all changes to medical requests
    """
    ACTION_CHOICES = [
        ('created', 'Created'),
        ('updated', 'Updated'),
        ('submitted', 'Submitted'),
        ('reviewed', 'Reviewed'),
        ('returned', 'Returned'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid'),
        ('item_added', 'Item Added'),
        ('item_updated', 'Item Updated'),
        ('item_deleted', 'Item Deleted'),
        ('attachment_added', 'Attachment Added'),
        ('attachment_deleted', 'Attachment Deleted'),
        ('pool_deduction', 'Pool Deduction'),
        ('pool_refund', 'Pool Refund'),
        ('error', 'Error'),
    ]

    request = models.ForeignKey(MedicalRequest, on_delete=models.CASCADE, related_name='audit_logs')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField()
    old_values = models.JSONField(null=True, blank=True, help_text="Previous values before change")
    new_values = models.JSONField(null=True, blank=True, help_text="New values after change")
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'request_audit_logs'
        ordering = ['-created_at']
        verbose_name = 'Request Audit Log'
        verbose_name_plural = 'Request Audit Logs'

    def __str__(self):
        return f"{self.request.request_number} - {self.action} by {self.user.username}"


class SystemConfiguration(models.Model):
    """
    System-wide configuration settings
    """
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    class Meta:
        db_table = 'system_configurations'
        verbose_name = 'System Configuration'
        verbose_name_plural = 'System Configurations'

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"


class RequestDeadline(models.Model):
    """
    Monthly deadlines for request submissions
    """
    month = models.DateField(unique=True)
    submission_deadline = models.DateTimeField()
    review_deadline = models.DateTimeField()
    approval_deadline = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    class Meta:
        db_table = 'request_deadlines'
        ordering = ['-month']
        verbose_name = 'Request Deadline'
        verbose_name_plural = 'Request Deadlines'

    def __str__(self):
        return f"Deadlines for {self.month.strftime('%B %Y')}"


# Signal handlers for automatic pool deduction
@receiver(pre_save, sender=MedicalRequest)
def track_status_change(sender, instance, **kwargs):
    """Track status changes to handle pool deductions"""
    if instance.pk:  # Only for existing instances
        try:
            old_instance = MedicalRequest.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except MedicalRequest.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None


@receiver(post_save, sender=MedicalRequest)
def handle_request_approval(sender, instance, created, **kwargs):
    """Automatically deduct costs from medical pools when request is approved"""
    if not created and hasattr(instance, '_old_status'):
        old_status = instance._old_status
        new_status = instance.status

        # Check if status changed from non-approved to approved
        if old_status != 'approved' and new_status == 'approved':
            deduct_from_medical_pools(instance)

        # Check if status changed from approved to non-approved (refund)
        elif old_status == 'approved' and new_status != 'approved':
            refund_to_medical_pools(instance)


def deduct_from_medical_pools(request):
    """Deduct request costs from employee and dependent medical pools"""
    from django.db import transaction

    with transaction.atomic():
        # Get all items for this request
        items = request.items.all()

        # Calculate totals by beneficiary
        employee_total = 0
        dependent_totals = {}

        for item in items:
            if item.dependent:
                # Dependent item
                if item.dependent.id not in dependent_totals:
                    dependent_totals[item.dependent.id] = 0
                dependent_totals[item.dependent.id] += item.cost
            else:
                # Employee item
                employee_total += item.cost

        # Deduct from employee pool
        if employee_total > 0:
            employee = request.employee
            employee.used_medical_pool += employee_total
            employee.save(update_fields=['used_medical_pool'])

            # Log the deduction
            RequestAuditLog.objects.create(
                request=request,
                action='pool_deduction',
                description=f'Deducted {employee_total:.2f} EGP from employee medical pool',
                user=request.employee  # Use the employee as the user
            )

        # Deduct from dependent pools
        for dependent_id, total_cost in dependent_totals.items():
            try:
                dependent = Dependent.objects.get(id=dependent_id)
                dependent.used_medical_pool += total_cost
                dependent.save(update_fields=['used_medical_pool'])

                # Log the deduction
                RequestAuditLog.objects.create(
                    request=request,
                    action='pool_deduction',
                    description=f'Deducted {total_cost:.2f} EGP from dependent {dependent.name} medical pool',
                    user=request.employee  # Use the employee as the user
                )
            except Dependent.DoesNotExist:
                # Log error if dependent not found
                RequestAuditLog.objects.create(
                    request=request,
                    action='error',
                    description=f'Could not find dependent with ID {dependent_id} for pool deduction',
                    user=request.employee
                )


def refund_to_medical_pools(request):
    """Refund request costs back to employee and dependent medical pools"""
    from django.db import transaction

    with transaction.atomic():
        # Get all items for this request
        items = request.items.all()

        # Calculate totals by beneficiary
        employee_total = 0
        dependent_totals = {}

        for item in items:
            if item.dependent:
                # Dependent item
                if item.dependent.id not in dependent_totals:
                    dependent_totals[item.dependent.id] = 0
                dependent_totals[item.dependent.id] += item.cost
            else:
                # Employee item
                employee_total += item.cost

        # Refund to employee pool
        if employee_total > 0:
            employee = request.employee
            employee.used_medical_pool = max(0, employee.used_medical_pool - employee_total)
            employee.save(update_fields=['used_medical_pool'])

            # Log the refund
            RequestAuditLog.objects.create(
                request=request,
                action='pool_refund',
                description=f'Refunded {employee_total:.2f} EGP to employee medical pool',
                user=request.employee  # Use the employee as the user
            )

        # Refund to dependent pools
        for dependent_id, total_cost in dependent_totals.items():
            try:
                dependent = Dependent.objects.get(id=dependent_id)
                dependent.used_medical_pool = max(0, dependent.used_medical_pool - total_cost)
                dependent.save(update_fields=['used_medical_pool'])

                # Log the refund
                RequestAuditLog.objects.create(
                    request=request,
                    action='pool_refund',
                    description=f'Refunded {total_cost:.2f} EGP to dependent {dependent.name} medical pool',
                    user=request.employee  # Use the employee as the user
                )
            except Dependent.DoesNotExist:
                # Log error if dependent not found
                RequestAuditLog.objects.create(
                    request=request,
                    action='error',
                    description=f'Could not find dependent with ID {dependent_id} for pool refund',
                    user=request.employee
                )
