# Medical Compensation Management System

A comprehensive intranet-based web application to automate and manage the monthly medical compensation request process for UGDC employees and their eligible dependents.

## Features

### 🔐 Role-Based Access Control
- **Employee (Requester)**: Submit & track requests, receive notifications
- **Medical Assistant (Reviewer)**: Review requests, return for corrections, manage pool limits
- **Medical Poss (Approver)**: Final approval/rejection, system monitoring
- **System Admin**: User management, system configuration, deadline setup

### 📑 Key Modules
- Authentication & Role-based Access
- Employee Dashboard with request tracking
- Dependents Management
- Medical Compensation Request Form
- Medical Department Review Interface
- Notifications & Alerts System
- Admin Configuration Panel
- Reporting and Analytics

## Tech Stack

### Backend
- **Framework**: Django 5.2.4
- **API**: Django REST Framework
- **Database**: PostgreSQL
- **Task Queue**: Celery with Redis
- **File Storage**: Local filesystem (upgradeable to S3)

### Frontend
- **Framework**: React with Vite
- **UI Library**: Material-UI or Tailwind CSS
- **State Management**: Redux Toolkit or <PERSON>ustand

## Project Structure

```
medical_compensation/
├── authentication/          # User authentication app
├── users/                  # User management and profiles
├── requests/               # Medical compensation requests
├── notifications/          # Email and in-app notifications
├── medical_compensation/   # Main Django project settings
├── frontend/              # React frontend application
├── static/                # Static files (CSS, JS, Images)
├── media/                 # User uploaded files
├── templates/             # Django templates
├── logs/                  # Application logs
└── requirements.txt       # Python dependencies
```

## Setup Instructions

### Prerequisites
- Python 3.11+
- PostgreSQL 12+
- Redis (for Celery)
- Node.js 18+ (for frontend)

### Backend Setup

1. **Clone and setup virtual environment**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   # source venv/bin/activate  # Linux/Mac
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your database and email settings
   ```

4. **Database setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   python manage.py createsuperuser
   ```

5. **Run development server**
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

## Development Workflow

1. **Database Migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

2. **Create Django Apps**
   ```bash
   python manage.py startapp app_name
   ```

3. **Run Tests**
   ```bash
   python manage.py test
   ```

4. **Collect Static Files**
   ```bash
   python manage.py collectstatic
   ```

## Security Features

- HTTPS with internal SSL certificate
- File size and type validation
- Role-based access control
- CSRF protection
- SQL injection prevention
- Secure file upload handling

## Contributing

1. Create feature branch from main
2. Make changes with proper tests
3. Submit pull request for review
4. Ensure all tests pass before merging

## License

Internal UGDC Project - All Rights Reserved
