<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="graddefault" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
        </linearGradient>
    </defs>
    <circle cx="40" cy="40" r="40" fill="url(#graddefault)" />
    <text x="40" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" 
          text-anchor="middle" fill="white">👤</text>
</svg>