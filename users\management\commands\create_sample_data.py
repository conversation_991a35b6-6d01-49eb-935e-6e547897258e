from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from users.models import User, Dependent
from requests.models import MedicalRequest, RequestItem, RequestDeadline
from notifications.models import Notification
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create sample data for testing the Employee Dashboard'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create sample employees
        self.create_employees()
        
        # Create sample dependents
        self.create_dependents()
        
        # Create sample deadlines
        self.create_deadlines()
        
        # Create sample requests
        self.create_requests()
        
        # Create sample notifications
        self.create_notifications()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )

    def create_employees(self):
        """Create sample employees"""
        employees_data = [
            {
                'username': 'john.doe',
                'email': '<EMAIL>',
                'employee_id': 'EMP001',
                'first_name': '<PERSON>',
                'last_name': 'Do<PERSON>',
                'role': 'employee',
                'department': 'engineering',
                'annual_medical_pool': 120000.00,  # 120,000 EGP
                'used_medical_pool': 36000.00,   # 36,000 EGP
            },
            {
                'username': 'jane.smith',
                'email': '<EMAIL>',
                'employee_id': 'EMP002',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'role': 'employee',
                'department': 'hr',
                'annual_medical_pool': 105000.00,  # 105,000 EGP
                'used_medical_pool': 24000.00,   # 24,000 EGP
            },
            {
                'username': 'medical.assistant',
                'email': '<EMAIL>',
                'employee_id': 'MED001',
                'first_name': 'Medical',
                'last_name': 'Assistant',
                'role': 'medical_assistant',
                'department': 'medical',
                'annual_medical_pool': 150000.00,  # 150,000 EGP
                'used_medical_pool': 0.00,
            },
        ]
        
        for emp_data in employees_data:
            user, created = User.objects.get_or_create(
                username=emp_data['username'],
                defaults=emp_data
            )
            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(f'Created employee: {user.username}')

    def create_dependents(self):
        """Create sample dependents"""
        try:
            john = User.objects.get(username='john.doe')
            jane = User.objects.get(username='jane.smith')
            
            dependents_data = [
                {
                    'employee': john,
                    'name': 'Mary Doe',
                    'relationship': 'wife',
                    'date_of_birth': datetime(1985, 5, 15).date(),
                    'annual_medical_pool': 90000.00,   # 90,000 EGP
                    'used_medical_pool': 15000.00,   # 15,000 EGP
                },
                {
                    'employee': john,
                    'name': 'Robert Doe Sr.',
                    'relationship': 'father',
                    'date_of_birth': datetime(1955, 3, 10).date(),
                    'annual_medical_pool': 75000.00,   # 75,000 EGP
                    'used_medical_pool': 36000.00,   # 36,000 EGP
                },
                {
                    'employee': jane,
                    'name': 'Michael Smith',
                    'relationship': 'son',
                    'date_of_birth': datetime(2010, 8, 22).date(),
                    'annual_medical_pool': 60000.00,   # 60,000 EGP
                    'used_medical_pool': 9000.00,    # 9,000 EGP
                },
            ]
            
            for dep_data in dependents_data:
                dependent, created = Dependent.objects.get_or_create(
                    employee=dep_data['employee'],
                    relationship=dep_data['relationship'],
                    defaults=dep_data
                )
                if created:
                    self.stdout.write(f'Created dependent: {dependent.name}')
                    
        except User.DoesNotExist:
            self.stdout.write('Employees not found, skipping dependents creation')

    def create_deadlines(self):
        """Create sample deadlines"""
        current_date = timezone.now()
        
        # Current month deadline
        current_month = current_date.replace(day=1)
        deadline, created = RequestDeadline.objects.get_or_create(
            month=current_month,
            defaults={
                'submission_deadline': current_month.replace(day=25, hour=23, minute=59),
                'review_deadline': current_month.replace(day=28, hour=23, minute=59),
                'approval_deadline': current_month.replace(day=30, hour=23, minute=59),
                'created_by': User.objects.get(username='admin'),
            }
        )
        if created:
            self.stdout.write(f'Created deadline for {current_month.strftime("%B %Y")}')

    def create_requests(self):
        """Create sample medical requests"""
        try:
            john = User.objects.get(username='john.doe')
            jane = User.objects.get(username='jane.smith')
            
            # Create requests for different months
            current_date = timezone.now()
            last_month = (current_date.replace(day=1) - timedelta(days=1)).replace(day=1)
            
            requests_data = [
                {
                    'employee': john,
                    'month': last_month,
                    'status': 'approved',
                    'employee_normal_total': 15000.00,  # 15,000 EGP
                    'employee_chronic_total': 6000.00,   # 6,000 EGP
                    'dependents_normal_total': 9000.00,  # 9,000 EGP
                    'dependents_chronic_total': 0.00,
                },
                {
                    'employee': jane,
                    'month': last_month,
                    'status': 'under_review',
                    'employee_normal_total': 12000.00,  # 12,000 EGP
                    'employee_chronic_total': 0.00,
                    'dependents_normal_total': 6000.00,  # 6,000 EGP
                    'dependents_chronic_total': 3000.00, # 3,000 EGP
                },
            ]
            
            for req_data in requests_data:
                request_obj, created = MedicalRequest.objects.get_or_create(
                    employee=req_data['employee'],
                    month=req_data['month'],
                    defaults=req_data
                )
                if created:
                    # Add some sample items
                    RequestItem.objects.create(
                        request=request_obj,
                        category='medicine',
                        type='normal',
                        beneficiary='employee',
                        description='Prescription medication',
                        cost=Decimal('4500.00'),  # 4,500 EGP
                        date_of_service=last_month.date(),
                    )
                    self.stdout.write(f'Created request: {request_obj.request_number}')
                    
        except User.DoesNotExist:
            self.stdout.write('Employees not found, skipping requests creation')

    def create_notifications(self):
        """Create sample notifications"""
        try:
            john = User.objects.get(username='john.doe')
            jane = User.objects.get(username='jane.smith')
            
            notifications_data = [
                {
                    'recipient': john,
                    'type': 'deadline_reminder',
                    'priority': 'medium',
                    'title': 'Submission Deadline Reminder',
                    'message': 'Don\'t forget to submit your medical compensation request before the deadline.',
                },
                {
                    'recipient': jane,
                    'type': 'request_approved',
                    'priority': 'high',
                    'title': 'Request Approved',
                    'message': 'Your medical compensation request has been approved.',
                },
            ]
            
            for notif_data in notifications_data:
                notification, created = Notification.objects.get_or_create(
                    recipient=notif_data['recipient'],
                    title=notif_data['title'],
                    defaults=notif_data
                )
                if created:
                    self.stdout.write(f'Created notification: {notification.title}')
                    
        except User.DoesNotExist:
            self.stdout.write('Employees not found, skipping notifications creation')
