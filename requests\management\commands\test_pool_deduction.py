from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from requests.models import MedicalRequest
from users.models import Dependent

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the automatic pool deduction functionality'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing automatic pool deduction...'))
        
        # Find a test request
        test_request = MedicalRequest.objects.filter(status='submitted').first()
        
        if not test_request:
            self.stdout.write(self.style.ERROR('No submitted requests found for testing'))
            return
        
        # Show current pool status
        employee = test_request.employee
        self.stdout.write(f'\nEmployee: {employee.get_full_name()}')
        self.stdout.write(f'Current used pool: {employee.used_medical_pool} EGP')
        
        # Show dependent pools if any
        dependents_in_request = []
        for item in test_request.items.all():
            if item.dependent and item.dependent not in dependents_in_request:
                dependents_in_request.append(item.dependent)
                self.stdout.write(f'Dependent {item.dependent.name}: {item.dependent.used_medical_pool} EGP used')
        
        # Calculate total cost
        total_cost = sum(item.cost for item in test_request.items.all())
        self.stdout.write(f'Request total cost: {total_cost} EGP')
        
        # Approve the request (this should trigger pool deduction)
        self.stdout.write(f'\nApproving request {test_request.id}...')
        test_request.status = 'approved'
        test_request.save()
        
        # Show updated pool status
        employee.refresh_from_db()
        self.stdout.write(f'\nAfter approval:')
        self.stdout.write(f'Employee used pool: {employee.used_medical_pool} EGP')
        
        for dependent in dependents_in_request:
            dependent.refresh_from_db()
            self.stdout.write(f'Dependent {dependent.name}: {dependent.used_medical_pool} EGP used')
        
        self.stdout.write(self.style.SUCCESS('\nPool deduction test completed!'))
