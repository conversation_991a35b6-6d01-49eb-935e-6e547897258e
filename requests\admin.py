from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    MedicalRequest, RequestItem, RequestAttachment,
    RequestAuditLog, SystemConfiguration, RequestDeadline
)


class RequestItemInline(admin.TabularInline):
    """Inline admin for request items"""
    model = RequestItem
    extra = 0
    fields = ['category', 'type', 'beneficiary', 'dependent', 'description', 'cost', 'date_of_service', 'is_flagged']
    readonly_fields = ['is_flagged']


class RequestAttachmentInline(admin.TabularInline):
    """Inline admin for request attachments"""
    model = RequestAttachment
    extra = 0
    fields = ['attachment_type', 'file', 'description', 'is_verified']
    readonly_fields = ['file_size', 'is_verified']


@admin.register(MedicalRequest)
class MedicalRequestAdmin(admin.ModelAdmin):
    """
    Admin interface for Medical Requests
    """
    list_display = [
        'request_number', 'employee', 'month', 'status',
        'total_amount_display', 'submitted_at_display', 'reviewed_by'
    ]
    list_filter = [
        'status', 'month', 'employee__department',
        'submitted_at', 'reviewed_at', 'approved_at'
    ]
    search_fields = [
        'request_number', 'employee__employee_id',
        'employee__first_name', 'employee__last_name'
    ]
    ordering = ['-created_at']
    readonly_fields = [
        'id', 'request_number', 'total_amount', 'employee_total',
        'dependents_total', 'created_at', 'updated_at', 'submitted_at'
    ]

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'request_number', 'employee', 'month', 'status')
        }),
        ('Amounts', {
            'fields': (
                'employee_normal_total', 'employee_chronic_total',
                'dependents_normal_total', 'dependents_chronic_total',
                'total_amount', 'employee_total', 'dependents_total'
            )
        }),
        ('Comments', {
            'fields': ('employee_notes', 'reviewer_comments', 'approver_comments')
        }),
        ('Review Information', {
            'fields': ('reviewed_by', 'reviewed_at', 'approved_by', 'approved_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'submitted_at')
        }),
    )

    inlines = [RequestItemInline, RequestAttachmentInline]

    def total_amount_display(self, obj):
        """Display total amount with currency formatting"""
        return "{:,.2f} EGP".format(obj.total_amount)
    total_amount_display.short_description = 'Total Amount'
    total_amount_display.admin_order_field = 'employee_normal_total'

    def submitted_at_display(self, obj):
        """Display submitted_at with proper formatting"""
        if obj.submitted_at:
            return obj.submitted_at.strftime('%Y-%m-%d %H:%M:%S')
        return '-'
    submitted_at_display.short_description = 'Submitted At'
    submitted_at_display.admin_order_field = 'submitted_at'


@admin.register(RequestItem)
class RequestItemAdmin(admin.ModelAdmin):
    """
    Admin interface for Request Items
    """
    list_display = [
        'request', 'category', 'type', 'beneficiary_name',
        'cost', 'date_of_service', 'is_flagged'
    ]
    list_filter = ['category', 'type', 'beneficiary', 'is_flagged', 'date_of_service']
    search_fields = ['request__request_number', 'description', 'dependent__name']
    ordering = ['-created_at']

    def beneficiary_name(self, obj):
        """Display beneficiary name"""
        if obj.beneficiary == 'dependent' and obj.dependent:
            return obj.dependent.name
        return obj.request.employee.get_full_name()
    beneficiary_name.short_description = 'Beneficiary'


@admin.register(RequestAttachment)
class RequestAttachmentAdmin(admin.ModelAdmin):
    """
    Admin interface for Request Attachments
    """
    list_display = [
        'request', 'attachment_type', 'file_name',
        'file_size_display', 'is_verified', 'created_at'
    ]
    list_filter = ['attachment_type', 'is_verified', 'created_at']
    search_fields = ['request__request_number', 'description']
    ordering = ['-created_at']
    readonly_fields = ['file_size']

    def file_name(self, obj):
        """Display file name"""
        return obj.file.name.split('/')[-1] if obj.file else 'No file'
    file_name.short_description = 'File Name'

    def file_size_display(self, obj):
        """Display file size in human readable format"""
        if obj.file_size:
            if obj.file_size < 1024:
                return "{} B".format(obj.file_size)
            elif obj.file_size < 1024 * 1024:
                return "{:.1f} KB".format(obj.file_size / 1024)
            else:
                return "{:.1f} MB".format(obj.file_size / (1024 * 1024))
        return "Unknown"
    file_size_display.short_description = 'File Size'


@admin.register(RequestAuditLog)
class RequestAuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for Request Audit Logs
    """
    list_display = ['request', 'user', 'action', 'created_at']
    list_filter = ['action', 'created_at', 'user__role']
    search_fields = ['request__request_number', 'user__username', 'description']
    ordering = ['-created_at']
    readonly_fields = ['created_at']


@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for System Configuration
    """
    list_display = ['key', 'value_preview', 'is_active', 'updated_at', 'updated_by']
    list_filter = ['is_active', 'updated_at']
    search_fields = ['key', 'description']
    ordering = ['key']
    readonly_fields = ['created_at', 'updated_at']

    def value_preview(self, obj):
        """Display truncated value"""
        return obj.value[:50] + "..." if len(obj.value) > 50 else obj.value
    value_preview.short_description = 'Value'


@admin.register(RequestDeadline)
class RequestDeadlineAdmin(admin.ModelAdmin):
    """
    Admin interface for Request Deadlines
    """
    list_display = [
        'month', 'submission_deadline', 'review_deadline',
        'approval_deadline', 'is_active'
    ]
    list_filter = ['is_active', 'month']
    ordering = ['-month']
    readonly_fields = ['created_at', 'updated_at']
