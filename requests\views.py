from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
from .models import MedicalRequest, RequestItem, RequestAttachment, RequestAuditLog
from users.models import Dependent
import json


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def request_list(request):
    """
    List user's medical requests with filtering and pagination
    """
    user = request.user

    # Get query parameters
    status_filter = request.GET.get('status', None)
    month_filter = request.GET.get('month', None)
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))

    # Build query
    queryset = MedicalRequest.objects.filter(employee=user)

    if status_filter:
        queryset = queryset.filter(status=status_filter)

    if month_filter:
        try:
            month_date = timezone.datetime.strptime(month_filter, '%Y-%m').date()
            queryset = queryset.filter(month__year=month_date.year, month__month=month_date.month)
        except ValueError:
            pass

    # Order by creation date (newest first)
    queryset = queryset.order_by('-created_at')

    # Pagination
    total_count = queryset.count()
    start_index = (page - 1) * page_size
    end_index = start_index + page_size
    requests_page = queryset[start_index:end_index]

    # Serialize data
    requests_data = []
    for req in requests_page:
        requests_data.append({
            'id': str(req.id),
            'request_number': req.request_number,
            'month': req.month.strftime('%Y-%m'),
            'month_display': req.month.strftime('%B %Y'),
            'status': req.status,
            'status_display': req.get_status_display(),
            'employee_total': float(req.employee_total),
            'dependents_total': float(req.dependents_total),
            'total_amount': float(req.total_amount),
            'created_at': req.created_at,
            'submitted_at': req.submitted_at,
            'reviewed_at': req.reviewed_at,
            'approved_at': req.approved_at,
            'reviewer_comments': req.reviewer_comments,
            'approver_comments': req.approver_comments,
        })

    return Response({
        'results': requests_data,  # Changed from 'requests' to 'results' for standard DRF format
        'count': total_count,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_pages': (total_count + page_size - 1) // page_size,
            'has_next': end_index < total_count,
            'has_previous': page > 1,
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def request_detail(request, request_id):
    """
    Get detailed information about a specific request
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    # Get request items
    items = RequestItem.objects.filter(request=medical_request).order_by('created_at')
    items_data = []
    for item in items:
        items_data.append({
            'id': item.id,
            'category': item.category,
            'category_display': item.get_category_display(),
            'type': item.type,
            'type_display': item.get_type_display(),
            'beneficiary': item.beneficiary,
            'beneficiary_display': item.get_beneficiary_display(),
            'dependent_name': item.dependent.name if item.dependent else None,
            'description': item.description,
            'cost': float(item.cost),
            'date_of_service': item.date_of_service,
            'is_flagged': item.is_flagged,
            'flag_reason': item.flag_reason,
        })

    # Get attachments
    attachments = RequestAttachment.objects.filter(request=medical_request).order_by('created_at')
    attachments_data = []
    for attachment in attachments:
        attachments_data.append({
            'id': attachment.id,
            'attachment_type': attachment.attachment_type,
            'attachment_type_display': attachment.get_attachment_type_display(),
            'file_name': attachment.file.name.split('/')[-1] if attachment.file else None,
            'file_url': attachment.file.url if attachment.file else None,
            'description': attachment.description,
            'file_size': attachment.file_size,
            'is_verified': attachment.is_verified,
            'created_at': attachment.created_at,
        })

    return Response({
        'request': {
            'id': str(medical_request.id),
            'request_number': medical_request.request_number,
            'month': medical_request.month.strftime('%Y-%m'),
            'month_display': medical_request.month.strftime('%B %Y'),
            'status': medical_request.status,
            'status_display': medical_request.get_status_display(),
            'employee_normal_total': float(medical_request.employee_normal_total),
            'employee_chronic_total': float(medical_request.employee_chronic_total),
            'dependents_normal_total': float(medical_request.dependents_normal_total),
            'dependents_chronic_total': float(medical_request.dependents_chronic_total),
            'employee_total': float(medical_request.employee_total),
            'dependents_total': float(medical_request.dependents_total),
            'total_amount': float(medical_request.total_amount),
            'employee_notes': medical_request.employee_notes,
            'reviewer_comments': medical_request.reviewer_comments,
            'approver_comments': medical_request.approver_comments,
            'created_at': medical_request.created_at,
            'submitted_at': medical_request.submitted_at,
            'reviewed_at': medical_request.reviewed_at,
            'approved_at': medical_request.approved_at,
        },
        'items': items_data,
        'attachments': attachments_data,
    })


@csrf_exempt
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_request(request):
    """
    Create a new medical request (draft)
    """
    user = request.user

    # Check if user can submit requests
    if not user.can_submit_request():
        return Response({
            'error': 'You are not authorized to submit requests'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        data = request.data
        month_str = data.get('month')  # Expected format: 'YYYY-MM'

        if not month_str:
            return Response({
                'error': 'Month is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Parse month
        try:
            month_date = timezone.datetime.strptime(month_str, '%Y-%m').date().replace(day=1)
        except ValueError:
            return Response({
                'error': 'Invalid month format. Use YYYY-MM'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user already has a request for this month
        existing_request = MedicalRequest.objects.filter(
            employee=user,
            month=month_date
        ).first()

        if existing_request:
            return Response({
                'error': f'You already have a request for {month_date.strftime("%B %Y")}',
                'existing_request_id': str(existing_request.id)
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create new request
        with transaction.atomic():
            medical_request = MedicalRequest.objects.create(
                employee=user,
                month=month_date,
                status='draft',
                employee_notes=data.get('employee_notes', '')
            )

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=user,
                action='created',
                description=f'Request created for {month_date.strftime("%B %Y")}'
            )

        return Response({
            'success': True,
            'message': 'Request created successfully',
            'request': {
                'id': str(medical_request.id),
                'request_number': medical_request.request_number,
                'month': medical_request.month.strftime('%Y-%m'),
                'month_display': medical_request.month.strftime('%B %Y'),
                'status': medical_request.status,
                'created_at': medical_request.created_at,
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': 'An error occurred while creating the request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_request(request, request_id):
    """
    Update a medical request (only if in draft status)
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    # Check if request can be updated
    if medical_request.status != 'draft':
        return Response({
            'error': 'Only draft requests can be updated'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        data = request.data

        # Update allowed fields
        if 'employee_notes' in data:
            medical_request.employee_notes = data['employee_notes']

        medical_request.save()

        # Create audit log
        RequestAuditLog.objects.create(
            request=medical_request,
            user=request.user,
            action='updated',
            description='Request updated'
        )

        return Response({
            'success': True,
            'message': 'Request updated successfully'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while updating the request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_request(request, request_id):
    """
    Submit a medical request for review
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    # Check if request can be submitted
    if medical_request.status != 'draft':
        return Response({
            'error': 'Only draft requests can be submitted'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Check if request has items
    if not medical_request.items.exists():
        return Response({
            'error': 'Request must have at least one item before submission'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        with transaction.atomic():
            # Update status and submission time
            medical_request.status = 'submitted'
            medical_request.submitted_at = timezone.now()
            medical_request.save()

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=request.user,
                action='submitted',
                description='Request submitted for review'
            )

        return Response({
            'success': True,
            'message': 'Request submitted successfully'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while submitting the request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_request_item(request, request_id):
    """
    Add an item to a medical request
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    # Check if request can be modified
    if medical_request.status not in ['draft']:
        return Response({
            'error': 'Only draft requests can be modified'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        data = request.data

        # Validate required fields
        required_fields = ['category', 'type', 'beneficiary', 'description', 'cost', 'date_of_service']
        for field in required_fields:
            if field not in data or not data[field]:
                return Response({
                    'error': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Validate dependent if beneficiary is dependent
        dependent = None
        if data['beneficiary'] == 'dependent':
            dependent_id = data.get('dependent_id')
            if not dependent_id:
                return Response({
                    'error': 'dependent_id is required when beneficiary is dependent'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                dependent = Dependent.objects.get(
                    id=dependent_id,
                    employee=request.user,
                    is_active=True
                )
            except Dependent.DoesNotExist:
                return Response({
                    'error': 'Invalid dependent'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Create the item
        with transaction.atomic():
            item = RequestItem.objects.create(
                request=medical_request,
                category=data['category'],
                type=data['type'],
                beneficiary=data['beneficiary'],
                dependent=dependent,
                description=data['description'],
                cost=data['cost'],
                date_of_service=data['date_of_service']
            )

            # Update request totals
            update_request_totals(medical_request)

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=request.user,
                action='item_added',
                description=f'Added {data["category"]} item for {data["beneficiary"]}'
            )

        return Response({
            'success': True,
            'message': 'Item added successfully',
            'item': {
                'id': item.id,
                'category': item.category,
                'type': item.type,
                'beneficiary': item.beneficiary,
                'dependent_name': item.dependent.name if item.dependent else None,
                'description': item.description,
                'cost': float(item.cost),
                'date_of_service': item.date_of_service,
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': 'An error occurred while adding the item'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_request_item(request, request_id, item_id):
    """
    Update a request item
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    item = get_object_or_404(RequestItem, id=item_id, request=medical_request)

    # Check if request can be modified
    if medical_request.status not in ['draft']:
        return Response({
            'error': 'Only draft requests can be modified'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        data = request.data

        # Update allowed fields
        if 'description' in data:
            item.description = data['description']
        if 'cost' in data:
            item.cost = data['cost']
        if 'date_of_service' in data:
            item.date_of_service = data['date_of_service']

        item.save()

        # Update request totals
        update_request_totals(medical_request)

        # Create audit log
        RequestAuditLog.objects.create(
            request=medical_request,
            user=request.user,
            action='item_updated',
            description=f'Updated {item.category} item'
        )

        return Response({
            'success': True,
            'message': 'Item updated successfully'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while updating the item'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_request_item(request, request_id, item_id):
    """
    Delete a request item
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    item = get_object_or_404(RequestItem, id=item_id, request=medical_request)

    # Check if request can be modified
    if medical_request.status not in ['draft']:
        return Response({
            'error': 'Only draft requests can be modified'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        with transaction.atomic():
            item_description = f'{item.category} item'
            item.delete()

            # Update request totals
            update_request_totals(medical_request)

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=request.user,
                action='item_deleted',
                description=f'Deleted {item_description}'
            )

        return Response({
            'success': True,
            'message': 'Item deleted successfully'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while deleting the item'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def update_request_totals(medical_request):
    """
    Update the total amounts for a medical request
    """
    items = medical_request.items.all()

    # Initialize totals
    employee_normal_total = 0
    employee_chronic_total = 0
    dependents_normal_total = 0
    dependents_chronic_total = 0

    # Calculate totals
    for item in items:
        if item.beneficiary == 'employee':
            if item.type == 'normal':
                employee_normal_total += item.cost
            else:  # chronic
                employee_chronic_total += item.cost
        else:  # dependent
            if item.type == 'normal':
                dependents_normal_total += item.cost
            else:  # chronic
                dependents_chronic_total += item.cost

    # Update the request
    medical_request.employee_normal_total = employee_normal_total
    medical_request.employee_chronic_total = employee_chronic_total
    medical_request.dependents_normal_total = dependents_normal_total
    medical_request.dependents_chronic_total = dependents_chronic_total
    medical_request.save()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_attachment(request, request_id):
    """
    Upload an attachment to a medical request
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    # Check if request can be modified
    if medical_request.status not in ['draft', 'submitted']:
        return Response({
            'error': 'Attachments can only be added to draft or submitted requests'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Get file and metadata
        uploaded_file = request.FILES.get('file')
        attachment_type = request.data.get('attachment_type')
        description = request.data.get('description', '')
        item_id = request.data.get('item_id')

        if not uploaded_file:
            return Response({
                'error': 'File is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not attachment_type:
            return Response({
                'error': 'Attachment type is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file size (10MB limit)
        max_size = 10 * 1024 * 1024  # 10MB
        if uploaded_file.size > max_size:
            return Response({
                'error': 'File size must be less than 10MB'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate file type
        allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx']
        file_extension = uploaded_file.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            return Response({
                'error': f'File type not allowed. Allowed types: {", ".join(allowed_extensions)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get related item if specified
        item = None
        if item_id:
            try:
                item = RequestItem.objects.get(id=item_id, request=medical_request)
            except RequestItem.DoesNotExist:
                return Response({
                    'error': 'Invalid item ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Create attachment
        with transaction.atomic():
            attachment = RequestAttachment.objects.create(
                request=medical_request,
                item=item,
                file=uploaded_file,
                attachment_type=attachment_type,
                description=description,
                file_size=uploaded_file.size
            )

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=request.user,
                action='attachment_added',
                description=f'Added {attachment_type} attachment: {uploaded_file.name}'
            )

        return Response({
            'success': True,
            'message': 'Attachment uploaded successfully',
            'attachment': {
                'id': attachment.id,
                'attachment_type': attachment.attachment_type,
                'file_name': uploaded_file.name,
                'file_size': attachment.file_size,
                'description': attachment.description,
                'created_at': attachment.created_at,
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': 'An error occurred while uploading the attachment'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_attachment(request, request_id, attachment_id):
    """
    Delete an attachment from a medical request
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    attachment = get_object_or_404(
        RequestAttachment,
        id=attachment_id,
        request=medical_request
    )

    # Check if request can be modified
    if medical_request.status not in ['draft']:
        return Response({
            'error': 'Only draft requests can be modified'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        with transaction.atomic():
            file_name = attachment.file.name if attachment.file else 'Unknown file'
            attachment.delete()

            # Create audit log
            RequestAuditLog.objects.create(
                request=medical_request,
                user=request.user,
                action='attachment_deleted',
                description=f'Deleted attachment: {file_name}'
            )

        return Response({
            'success': True,
            'message': 'Attachment deleted successfully'
        })

    except Exception as e:
        return Response({
            'error': 'An error occurred while deleting the attachment'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def generate_coversheet(request, request_id):
    """
    Generate a coversheet for printing and attaching to original documents
    """
    medical_request = get_object_or_404(
        MedicalRequest,
        id=request_id,
        employee=request.user
    )

    try:
        from django.http import HttpResponse
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.units import inch
        from io import BytesIO

        # Create PDF
        buffer = BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        width, height = letter

        # Header
        p.setFont("Helvetica-Bold", 16)
        p.drawString(1*inch, height - 1*inch, "UGDC Medical Compensation Request")

        # Request Information
        p.setFont("Helvetica-Bold", 12)
        p.drawString(1*inch, height - 1.5*inch, "Request Information:")

        p.setFont("Helvetica", 10)
        y_position = height - 1.8*inch

        info_lines = [
            f"Request Number: {medical_request.request_number}",
            f"Employee: {medical_request.employee.get_full_name()} ({medical_request.employee.employee_id})",
            f"Department: {medical_request.employee.department}",
            f"Month: {medical_request.month.strftime('%B %Y')}",
            f"Status: {medical_request.get_status_display()}",
            f"Total Amount: {medical_request.total_amount:.2f} EGP",
            f"Generated: {timezone.now().strftime('%Y-%m-%d %H:%M')}",
        ]

        for line in info_lines:
            p.drawString(1*inch, y_position, line)
            y_position -= 0.3*inch

        # Items Summary
        y_position -= 0.5*inch
        p.setFont("Helvetica-Bold", 12)
        p.drawString(1*inch, y_position, "Items Summary:")
        y_position -= 0.3*inch

        p.setFont("Helvetica", 9)
        items = medical_request.items.all()
        for item in items:
            beneficiary = item.dependent.name if item.dependent else "Employee"
            item_line = f"• {item.get_category_display()} ({item.get_type_display()}) - {beneficiary} - {item.cost:.2f} EGP"
            p.drawString(1.2*inch, y_position, item_line)
            y_position -= 0.2*inch

            if y_position < 2*inch:  # Start new page if needed
                p.showPage()
                y_position = height - 1*inch

        # Footer
        p.setFont("Helvetica", 8)
        p.drawString(1*inch, 1*inch, "Please attach this coversheet to your original documents when submitting.")
        p.drawString(1*inch, 0.8*inch, "For office use only - Do not detach this coversheet.")

        p.showPage()
        p.save()

        # Return PDF response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="coversheet_{medical_request.request_number}.pdf"'

        return response

    except ImportError:
        return Response({
            'error': 'PDF generation not available. Please install reportlab.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        return Response({
            'error': 'An error occurred while generating the coversheet'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
