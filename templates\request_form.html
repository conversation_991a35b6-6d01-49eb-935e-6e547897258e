<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Compensation Request Form</title>
    <!-- Cache buster: Updated at 2025-01-04 15:30 - Fixed container IDs -->
    {% load static %}
    {% csrf_token %}
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .form-section {
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        .items-container {
            margin-top: 20px;
        }

        .item-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .item-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .item-type-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-normal {
            background: #d4edda;
            color: #155724;
        }

        .badge-chronic {
            background: #fff3cd;
            color: #856404;
        }

        .totals-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }

        .totals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .total-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .total-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .total-amount {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .grand-total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .attachments-section {
            margin-top: 20px;
        }

        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .attachment-list {
            margin-top: 20px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .attachment-info {
            display: flex;
            align-items: center;
        }

        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #667eea;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 12px;
        }

        /* Status badge styles */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin: 15px 0;
            border: 2px solid;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            animation: statusAppear 0.5s ease-out;
        }

        @keyframes statusAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .status-badge .status-icon {
            margin-right: 8px;
            font-size: 16px;
            font-weight: bold;
        }

        .status-badge .status-text {
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Status-specific colors */
        .status-draft {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-color: #17a2b8;
        }

        .status-submitted {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-color: #ffc107;
        }

        .status-approved {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-color: #28a745;
        }

        .status-rejected {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-color: #dc3545;
        }

        .status-paid {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #383d41;
            border-color: #6c757d;
        }

        /* Floating notification system */
        .floating-notifications {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            pointer-events: none;
        }

        .floating-alert {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            padding: 16px 20px;
            border-left: 4px solid;
            font-weight: 500;
            pointer-events: auto;
            transform: translateX(100%);
            transition: all 0.3s ease-in-out;
            position: relative;
            animation: slideInBounce 0.5s ease-out;
        }

        @keyframes slideInBounce {
            0% {
                transform: translateX(100%);
                opacity: 0;
            }
            60% {
                transform: translateX(-10px);
                opacity: 1;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .floating-alert.show {
            transform: translateX(0);
        }

        .floating-alert.success {
            border-left-color: #28a745;
            color: #155724;
        }

        .floating-alert.error {
            border-left-color: #dc3545;
            color: #721c24;
        }

        .floating-alert.warning {
            border-left-color: #ffc107;
            color: #856404;
        }

        .floating-alert .close-btn {
            position: absolute;
            top: 8px;
            right: 12px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
        }

        .floating-alert .close-btn:hover {
            opacity: 1;
        }

        /* Auto-hide animation */
        .floating-alert.fade-out {
            opacity: 0;
            transform: translateX(100%);
        }

        /* Deletion tracking styles */
        .item-marked-for-deletion {
            opacity: 0.6;
            background-color: #f8d7da !important;
            border: 2px dashed #dc3545 !important;
        }

        .deletion-notice {
            background: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 5px;
            border: 1px solid #f5c6cb;
        }

        /* Validation error styles */
        .field-error {
            border: 2px solid #dc3545 !important;
            background-color: #f8d7da !important;
        }

        .field-error:focus {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            display: block;
        }

        .hidden {
            display: none;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding: 25px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-section {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .totals-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Floating notifications container -->
    <div id="floatingNotifications" class="floating-notifications"></div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Medical Compensation Request</h1>
            <p>Submit your monthly medical expenses for reimbursement</p>
            <div id="requestStatus" class="status-badge" style="display: none;">
                <span class="status-icon" id="statusIcon"></span>
                <span class="status-text" id="statusText"></span>
            </div>
            <button onclick="testFunctions()" style="background: #ffc107; color: black; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                🧪 Test Functions
            </button>
            <button onclick="testMonthAPI()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px; margin-left: 10px;">
                🔍 Test Month API
            </button>
        </div>

        <!-- Alert Messages removed - using floating notifications only -->

        <!-- Request Form -->
        <div class="form-container">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">1</div>
                    <div class="section-title">Request Information</div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="requestMonth">Request Month *</label>
                        <input type="month" id="requestMonth" required onchange="checkExistingRequest()">
                        <small style="color: #6c757d;">Select the month for which you are submitting medical expenses</small>
                        <div id="monthCheckStatus" style="margin-top: 5px; font-size: 12px; display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="employeeNotes">Additional Notes</label>
                        <textarea id="employeeNotes" placeholder="Any additional information about your request..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Employee Medical Expenses Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">2</div>
                    <div class="section-title">Employee Medical Expenses</div>
                </div>

                <!-- Employee Normal Expenses -->
                <div style="margin-bottom: 30px;">
                    <h4 style="color: #28a745; margin-bottom: 15px;">Normal Medical Expenses</h4>
                    <div id="employeeNormalItems" class="items-container"></div>
                    <button type="button" class="btn btn-success btn-sm" onclick="window.addItem('employee', 'normal')">
                        + Add Normal Expense
                    </button>
                </div>

                <!-- Employee Chronic Expenses -->
                <div>
                    <h4 style="color: #ffc107; margin-bottom: 15px;">Chronic Medical Expenses</h4>
                    <div id="employeeChronicItems" class="items-container"></div>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="window.addItem('employee', 'chronic')">
                        + Add Chronic Expense
                    </button>
                </div>
            </div>

            <!-- Dependents Medical Expenses Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">3</div>
                    <div class="section-title">Dependents Medical Expenses</div>
                </div>

                <!-- Dependents Normal Expenses -->
                <div style="margin-bottom: 30px;">
                    <h4 style="color: #28a745; margin-bottom: 15px;">Normal Medical Expenses</h4>
                    <div id="dependentsNormalItems" class="items-container"></div>
                    <button type="button" class="btn btn-success btn-sm" onclick="window.addItem('dependent', 'normal')">
                        + Add Normal Expense
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="window.checkDependents()" style="margin-left: 10px;">
                        Check Dependents
                    </button>
                </div>

                <!-- Dependents Chronic Expenses -->
                <div>
                    <h4 style="color: #ffc107; margin-bottom: 15px;">Chronic Medical Expenses</h4>
                    <div id="dependentsChronicItems" class="items-container"></div>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="window.addItem('dependent', 'chronic')">
                        + Add Chronic Expense
                    </button>
                </div>
            </div>

            <!-- Attachments Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">4</div>
                    <div class="section-title">Supporting Documents</div>
                </div>

                <div class="attachments-section">
                    <div class="file-upload-area"
                         onclick="document.getElementById('fileInput').click()"
                         ondragover="handleDragOver(event)"
                         ondrop="handleDrop(event)"
                         ondragleave="handleDragLeave(event)"
                         ondragenter="handleDragOver(event)">
                        <div style="font-size: 48px; color: #667eea; margin-bottom: 15px;">📎</div>
                        <h4>Upload Supporting Documents</h4>
                        <p>Click here or drag and drop files</p>
                        <p style="font-size: 12px; color: #6c757d; margin-top: 10px;">
                            Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 10MB)
                        </p>
                        <div id="dragDropStatus" style="font-size: 12px; color: #28a745; margin-top: 10px; display: none;">
                            ✓ Drag and drop is working!
                        </div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">

                    <div id="attachmentsList" class="attachment-list"></div>
                </div>
            </div>

            <!-- Totals Section -->
            <div class="totals-section">
                <h3 style="text-align: center; margin-bottom: 25px; color: #2c3e50;">Request Summary</h3>
                <div class="totals-grid">
                    <div class="total-card">
                        <div class="total-label">Employee Normal</div>
                        <div class="total-amount" id="employeeNormalTotal">0.00 EGP</div>
                    </div>
                    <div class="total-card">
                        <div class="total-label">Employee Chronic</div>
                        <div class="total-amount" id="employeeChronicTotal">0.00 EGP</div>
                    </div>
                    <div class="total-card">
                        <div class="total-label">Dependents Normal</div>
                        <div class="total-amount" id="dependentsNormalTotal">0.00 EGP</div>
                    </div>
                    <div class="total-card">
                        <div class="total-label">Dependents Chronic</div>
                        <div class="total-amount" id="dependentsChronicTotal">0.00 EGP</div>
                    </div>
                    <div class="total-card grand-total">
                        <div class="total-label">Grand Total</div>
                        <div class="total-amount" id="grandTotal">0.00 EGP</div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="window.saveDraft()">
                    <span class="spinner hidden" id="draftSpinner"></span>
                    Save as Draft
                </button>
                <button type="button" class="btn btn-primary" onclick="window.generateCoversheet()" id="coversheetBtn" disabled>
                    Generate Coversheet
                </button>
                <button type="button" class="btn btn-primary" onclick="window.submitRequest()" id="submitBtn" disabled>
                    <span class="spinner hidden" id="submitSpinner"></span>
                    Submit Request
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let currentRequest = null;
        let dependents = [];
        let requestItems = [];
        let attachments = [];

        // Get CSRF token from cookies (Django's default method)
        function getCSRFToken() {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, 10) === 'csrftoken=') {
                        cookieValue = decodeURIComponent(cookie.substring(10));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Make functions globally accessible immediately
        window.addItem = function(beneficiary, type) {
            console.log(`Adding item: ${beneficiary} ${type}`);
            console.log('Available dependents:', dependents);

            // Check if dependents are loaded for dependent items
            if (beneficiary === 'dependent' && (!dependents || dependents.length === 0)) {
                showAlert('No dependents found. Please add dependents first or contact your administrator.', 'warning');
                return;
            }

            const itemId = Date.now(); // Temporary ID for new items
            // Fix container ID generation - add 's' for dependents
            const beneficiaryName = beneficiary === 'dependent' ? 'dependents' : beneficiary;
            const containerId = `${beneficiaryName}${type.charAt(0).toUpperCase() + type.slice(1)}Items`;
            console.log(`Generated container ID: ${containerId} (from beneficiary: ${beneficiary}, type: ${type})`);
            const container = document.getElementById(containerId);

            if (!container) {
                console.error(`Container not found: ${containerId}`);
                showAlert('Error: Could not find container for item', 'error');
                return;
            }

            // Generate dependent options
            let dependentOptions = '';
            if (beneficiary === 'dependent') {
                dependentOptions = dependents.map(dep =>
                    `<option value="${dep.id}">${dep.name} (${dep.relationship})</option>`
                ).join('');
            }

            const itemHtml = `
                <div class="item-card" data-item-id="${itemId}">
                    <div class="item-header">
                        <span class="item-type-badge badge-${type}">${type.toUpperCase()}</span>
                        <button type="button" class="btn btn-danger btn-sm" onclick="window.removeItem(${itemId})">Remove</button>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>Category *</label>
                            <select class="item-category" required onchange="updateTotals()">
                                <option value="">Select Category</option>
                                <option value="operation">Operation</option>
                                <option value="glasses">Glasses</option>
                                <option value="dental">Dental</option>
                                <option value="medicine">Medicine</option>
                                <option value="dl_services">D&L Services</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        ${beneficiary === 'dependent' ? `
                        <div class="form-group">
                            <label>Dependent *</label>
                            <select class="item-dependent" required onchange="updateTotals()">
                                <option value="">Select Dependent</option>
                                ${dependentOptions}
                            </select>
                        </div>
                        ` : ''}

                        <div class="form-group">
                            <label>Description *</label>
                            <input type="text" class="item-description" placeholder="Describe the medical expense..." required onchange="updateTotals()">
                        </div>

                        <div class="form-group">
                            <label>Cost (EGP) *</label>
                            <input type="number" class="item-cost" step="0.01" min="0" placeholder="0.00" required onchange="updateTotals()">
                        </div>

                        <div class="form-group">
                            <label>Date of Service *</label>
                            <input type="date" class="item-date" required onchange="updateTotals()">
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', itemHtml);
            updateTotals();
            updateFormButtons();

            console.log(`Item added successfully to ${containerId}`);
        };

        // Track items that need to be deleted from the database
        window.itemsToDelete = window.itemsToDelete || [];

        window.removeItem = function(itemId) {
            const itemCard = document.querySelector(`[data-item-id="${itemId}"]`);
            if (itemCard) {
                // If this item has an ID (exists in database), mark it for deletion
                if (itemId && !isNaN(itemId)) {
                    console.log(`Marking item ${itemId} for deletion`);
                    if (!window.itemsToDelete.includes(itemId)) {
                        window.itemsToDelete.push(itemId);
                    }

                    // Add visual indicator that item will be deleted
                    itemCard.classList.add('item-marked-for-deletion');

                    // Add deletion notice
                    const existingNotice = itemCard.querySelector('.deletion-notice');
                    if (!existingNotice) {
                        const notice = document.createElement('div');
                        notice.className = 'deletion-notice';
                        notice.innerHTML = '⚠️ This item will be permanently deleted when you save the draft';
                        itemCard.appendChild(notice);
                    }

                    // Change remove button text
                    const removeBtn = itemCard.querySelector('button');
                    if (removeBtn) {
                        removeBtn.textContent = 'Undo Delete';
                        removeBtn.onclick = () => undoDeleteItem(itemId);
                    }
                } else {
                    // New item (no ID) - just remove from DOM immediately
                    itemCard.remove();
                }

                updateTotals();
                updateFormButtons();

                console.log('Items marked for deletion:', window.itemsToDelete);
            }
        };

        window.undoDeleteItem = function(itemId) {
            const itemCard = document.querySelector(`[data-item-id="${itemId}"]`);
            if (itemCard) {
                // Remove from deletion list
                window.itemsToDelete = window.itemsToDelete.filter(id => id !== itemId);

                // Remove visual indicators
                itemCard.classList.remove('item-marked-for-deletion');
                const notice = itemCard.querySelector('.deletion-notice');
                if (notice) notice.remove();

                // Restore remove button
                const removeBtn = itemCard.querySelector('button');
                if (removeBtn) {
                    removeBtn.textContent = 'Remove';
                    removeBtn.onclick = () => window.removeItem(itemId);
                }

                console.log('Undid deletion for item:', itemId);
                console.log('Items marked for deletion:', window.itemsToDelete);
            }
        };

        window.checkDependents = function() {
            if (!dependents || dependents.length === 0) {
                showAlert('No dependents found. You may need to contact your administrator to add dependents to your account.', 'warning');
            } else {
                const dependentsList = dependents.map(dep => `${dep.name} (${dep.relationship})`).join(', ');
                showAlert(`Found ${dependents.length} dependents: ${dependentsList}`, 'success');
            }
            console.log('Current dependents:', dependents);
        };

        // Drag and drop functions (handleFileSelect moved to bottom of file)

        window.handleDragOver = function(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.add('dragover');

            // Show drag status
            const statusDiv = document.getElementById('dragDropStatus');
            if (statusDiv) {
                statusDiv.style.display = 'block';
                statusDiv.textContent = '📁 Drop files here...';
                statusDiv.style.color = '#667eea';
            }
        };

        window.handleDrop = function(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('dragover');

            // Update status
            const statusDiv = document.getElementById('dragDropStatus');
            if (statusDiv) {
                statusDiv.textContent = '✓ Files received!';
                statusDiv.style.color = '#28a745';
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }

            const files = event.dataTransfer.files;
            console.log('Files dropped:', files.length);
            handleFiles(files);
        };

        window.handleDragLeave = function(event) {
            event.preventDefault();
            event.stopPropagation();
            // Only remove dragover if we're actually leaving the drop zone
            if (!event.currentTarget.contains(event.relatedTarget)) {
                event.currentTarget.classList.remove('dragover');

                // Hide status
                const statusDiv = document.getElementById('dragDropStatus');
                if (statusDiv) {
                    statusDiv.style.display = 'none';
                }
            }
        };

        // Test function to verify everything is working
        window.testFunctions = function() {
            let results = [];

            // Test if functions exist
            results.push(`addItem function: ${typeof window.addItem === 'function' ? '✅ EXISTS' : '❌ MISSING'}`);
            results.push(`removeItem function: ${typeof window.removeItem === 'function' ? '✅ EXISTS' : '❌ MISSING'}`);
            results.push(`checkDependents function: ${typeof window.checkDependents === 'function' ? '✅ EXISTS' : '❌ MISSING'}`);
            results.push(`handleDragOver function: ${typeof window.handleDragOver === 'function' ? '✅ EXISTS' : '❌ MISSING'}`);

            // Test dependents data
            results.push(`Dependents loaded: ${dependents && dependents.length > 0 ? '✅ YES (' + dependents.length + ')' : '❌ NO'}`);

            // Test current request state
            results.push(`Current request: ${currentRequest ? '✅ SET (ID: ' + (currentRequest.id || 'NO ID') + ')' : '❌ NULL'}`);

            // Test containers
            const containers = ['employeeNormalItems', 'employeeChronicItems', 'dependentsNormalItems', 'dependentsChronicItems'];
            containers.forEach(id => {
                const element = document.getElementById(id);
                results.push(`Container ${id}: ${element ? '✅ FOUND' : '❌ MISSING'}`);
            });

            // Test drag drop area
            const uploadArea = document.querySelector('.file-upload-area');
            results.push(`Upload area: ${uploadArea ? '✅ FOUND' : '❌ MISSING'}`);

            alert('Function Test Results:\n\n' + results.join('\n'));
            console.log('Test results:', results);
        };

        // Reset current request (useful for testing)
        window.resetRequest = function() {
            currentRequest = null;
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('id');
            window.history.replaceState({}, '', newUrl);
            showAlert('Request state reset. You can now create a new request.', 'info');
        };

        // Test the month API to debug
        window.testMonthAPI = async function() {
            const monthInput = document.getElementById('requestMonth');
            if (!monthInput.value) {
                alert('Please select a month first');
                return;
            }

            try {
                const apiUrl = `/api/requests/?month=${monthInput.value}`;
                console.log('Testing API URL:', apiUrl);

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Full API Response:', data);

                    const requestsArray = data.results || data.requests || [];
                    let resultDetails = '';
                    if (requestsArray.length > 0) {
                        resultDetails = `\nFirst result:\n- ID: ${requestsArray[0].id}\n- Month: ${requestsArray[0].month}\n- Status: ${requestsArray[0].status}`;
                    }

                    alert(`API Test Results:\n\nURL: ${apiUrl}\nStatus: ${response.status}\nResults Count: ${data.results ? data.results.length : 'N/A'}\nRequests Count: ${data.requests ? data.requests.length : 'N/A'}\nTotal Count: ${data.total_count || 'N/A'}${resultDetails}\n\nCheck console for full response.`);
                } else {
                    const errorText = await response.text();
                    console.log('Error response:', errorText);
                    alert(`API Error: ${response.status} ${response.statusText}\n\nResponse: ${errorText.substring(0, 200)}...`);
                }
            } catch (error) {
                console.error('API test error:', error);
                alert(`Error: ${error.message}`);
            }
        };

        // Check for existing request when month is selected
        window.checkExistingRequest = async function() {
            const monthInput = document.getElementById('requestMonth');
            const statusDiv = document.getElementById('monthCheckStatus');

            if (!monthInput.value) {
                statusDiv.style.display = 'none';
                return;
            }

            if (!authToken) {
                statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Not logged in</span>';
                return;
            }

            try {
                statusDiv.style.display = 'block';
                statusDiv.innerHTML = '<span style="color: #007bff;">🔍 Checking for existing request...</span>';

                const apiUrl = `/api/requests/?month=${monthInput.value}`;
                console.log('Checking month API:', apiUrl);
                console.log('Auth token:', authToken ? 'Present' : 'Missing');

                // Check if there's already a request for this month
                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Month check response:', data);
                    console.log('Results count:', data.results ? data.results.length : 'No results property');
                    console.log('Requests count:', data.requests ? data.requests.length : 'No requests property');

                    // Check both possible property names (results or requests)
                    const requestsArray = data.results || data.requests || [];

                    if (requestsArray && requestsArray.length > 0) {
                        // Found existing request
                        const existingRequest = requestsArray[0];
                        statusDiv.innerHTML = '<span style="color: #ffc107;">⚠️ Request already exists for this month</span>';

                        const shouldLoad = confirm(
                            `You already have a request for ${monthInput.value}.\n\n` +
                            `Options:\n` +
                            `• Click OK to load the existing request (with all previously entered data)\n` +
                            `• Click Cancel to select a different month\n\n` +
                            `Would you like to load the existing request?`
                        );

                        if (shouldLoad) {
                            // Load the existing request with all its data
                            await loadExistingRequestComplete(existingRequest.id);
                        } else {
                            // Clear the month selection
                            monthInput.value = '';
                            statusDiv.style.display = 'none';
                            showAlert('Please select a different month for your new request.', 'warning');
                        }
                    } else {
                        // No existing request found
                        statusDiv.innerHTML = '<span style="color: #28a745;">✅ No existing request found - you can proceed</span>';

                        // Clear form for new request
                        clearFormForNewRequest();

                        setTimeout(() => {
                            statusDiv.style.display = 'none';
                        }, 3000);
                    }
                } else {
                    console.error('Month check failed:', response.status, response.statusText);
                    statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Error checking for existing request</span>';
                }
            } catch (error) {
                console.error('Error checking existing request:', error);
                statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Error checking for existing request</span>';
            }
        };

        // Load existing request for editing (basic version)
        async function loadExistingRequest(requestId) {
            try {
                showAlert('Loading existing request...', 'info');

                const response = await fetch(`/api/requests/${requestId}/`, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentRequest = data;

                    // Update form with existing data
                    document.getElementById('requestMonth').value = data.month;
                    document.getElementById('employeeNotes').value = data.employee_notes || '';

                    // Update URL
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.set('id', requestId);
                    window.history.replaceState({}, '', newUrl);

                    showAlert('Existing request loaded successfully!', 'success');
                    updateFormButtons();
                } else {
                    showAlert('Failed to load existing request', 'error');
                }
            } catch (error) {
                console.error('Error loading existing request:', error);
                showAlert('Error loading existing request', 'error');
            }
        }

        // Load existing request with ALL data (items, attachments, etc.)
        async function loadExistingRequestComplete(requestId) {
            try {
                showAlert('Loading existing request with all data...', 'info');

                // Load the main request data
                const response = await fetch(`/api/requests/${requestId}/`, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Load existing request response:', data);

                    // Extract request data from the response
                    const requestData = data.request || data;
                    const itemsData = data.items || [];
                    const attachmentsData = data.attachments || [];

                    currentRequest = requestData;

                    // Clear any pending deletions when loading a different request
                    window.itemsToDelete = [];

                    // Update form with existing data
                    document.getElementById('requestMonth').value = requestData.month;
                    document.getElementById('employeeNotes').value = requestData.employee_notes || '';

                    // Clear existing items
                    document.getElementById('employeeNormalItems').innerHTML = '';
                    document.getElementById('employeeChronicItems').innerHTML = '';
                    document.getElementById('dependentsNormalItems').innerHTML = '';
                    document.getElementById('dependentsChronicItems').innerHTML = '';

                    // Load and display existing items
                    if (itemsData && itemsData.length > 0) {
                        console.log(`Loading ${itemsData.length} existing items:`, itemsData);
                        for (const item of itemsData) {
                            await loadExistingItem(item);
                        }
                    }

                    // Clear and load existing attachments
                    document.getElementById('attachmentsList').innerHTML = '';
                    if (attachmentsData && attachmentsData.length > 0) {
                        console.log(`Loading ${attachmentsData.length} existing attachments:`, attachmentsData);
                        for (const attachment of attachmentsData) {
                            console.log('Loading attachment:', attachment);
                            addAttachmentToList(attachment);
                        }
                    } else {
                        console.log('No attachments found in response');
                    }

                    // Update URL
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.set('id', requestId);
                    window.history.replaceState({}, '', newUrl);

                    // Update totals and form state
                    updateTotals();
                    updateFormButtons();
                    updateRequestStatus();

                    const totalItems = itemsData.length;
                    const totalAttachments = attachmentsData.length;
                    showAlert(`Existing request loaded successfully with ${totalItems} items and ${totalAttachments} attachments!`, 'success');
                } else {
                    showAlert('Failed to load existing request', 'error');
                }
            } catch (error) {
                console.error('Error loading existing request:', error);
                showAlert('Error loading existing request', 'error');
            }
        }

        // Load and display an existing item
        async function loadExistingItem(item) {
            console.log('Loading existing item:', item);

            // Determine beneficiary based on item data
            const beneficiary = item.beneficiary || (item.dependent_name ? 'dependent' : 'employee');
            const type = item.type;

            // Generate container ID
            const beneficiaryName = beneficiary === 'dependent' ? 'dependents' : beneficiary;
            const containerId = `${beneficiaryName}${type.charAt(0).toUpperCase() + type.slice(1)}Items`;
            const container = document.getElementById(containerId);

            if (!container) {
                console.error(`Container not found for existing item: ${containerId}`);
                return;
            }

            // Generate dependent options if needed
            let dependentOptions = '';
            if (beneficiary === 'dependent') {
                dependentOptions = dependents.map(dep => {
                    // Check if this dependent matches by name or ID
                    const isSelected = (item.dependent_id && dep.id === item.dependent_id) ||
                                     (item.dependent_name && dep.name === item.dependent_name);
                    return `<option value="${dep.id}" ${isSelected ? 'selected' : ''}>${dep.name} (${dep.relationship})</option>`;
                }).join('');
            }

            const itemHtml = `
                <div class="item-card" data-item-id="${item.id}">
                    <div class="item-header">
                        <span class="item-type-badge badge-${type}">${type.toUpperCase()}</span>
                        <button type="button" class="btn btn-danger btn-sm" onclick="window.removeItem(${item.id})">Remove</button>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>Category *</label>
                            <select class="item-category" required onchange="updateTotals()">
                                <option value="">Select Category</option>
                                <option value="operation" ${item.category === 'operation' ? 'selected' : ''}>Operation</option>
                                <option value="glasses" ${item.category === 'glasses' ? 'selected' : ''}>Glasses</option>
                                <option value="dental" ${item.category === 'dental' ? 'selected' : ''}>Dental</option>
                                <option value="medicine" ${item.category === 'medicine' ? 'selected' : ''}>Medicine</option>
                                <option value="dl_services" ${item.category === 'dl_services' ? 'selected' : ''}>D&L Services</option>
                                <option value="other" ${item.category === 'other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>

                        ${beneficiary === 'dependent' ? `
                        <div class="form-group">
                            <label>Dependent *</label>
                            <select class="item-dependent" required onchange="updateTotals()">
                                <option value="">Select Dependent</option>
                                ${dependentOptions}
                            </select>
                        </div>
                        ` : ''}

                        <div class="form-group">
                            <label>Description *</label>
                            <input type="text" class="item-description" placeholder="Describe the medical expense..." required onchange="updateTotals()" value="${item.description || ''}">
                        </div>

                        <div class="form-group">
                            <label>Cost (EGP) *</label>
                            <input type="number" class="item-cost" step="0.01" min="0" placeholder="0.00" required onchange="updateTotals()" value="${item.cost || ''}">
                        </div>

                        <div class="form-group">
                            <label>Date of Service *</label>
                            <input type="date" class="item-date" required onchange="updateTotals()" value="${item.date_of_service || ''}">
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', itemHtml);
        }

        // Initialize the form
        document.addEventListener('DOMContentLoaded', function() {
            if (!authToken) {
                showAlert('Please login first', 'error');
                setTimeout(() => {
                    window.location.href = '/dashboard-test/';
                }, 2000);
                return;
            }

            loadUserData().then(() => {
                setupEventListeners();

                // Check if editing existing request
                const urlParams = new URLSearchParams(window.location.search);
                const requestId = urlParams.get('id');
                if (requestId) {
                    loadExistingRequest(requestId);
                } else {
                    // No existing request - ensure clean state
                    clearFormForNewRequest();
                }
            });
        });

        function setupEventListeners() {
            // Clear validation errors when user starts typing/selecting
            document.addEventListener('input', function(e) {
                if (e.target.classList.contains('field-error')) {
                    e.target.classList.remove('field-error');
                    const errorMsg = e.target.parentNode.querySelector('.error-message');
                    if (errorMsg) errorMsg.remove();
                }
            });

            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('field-error')) {
                    e.target.classList.remove('field-error');
                    const errorMsg = e.target.parentNode.querySelector('.error-message');
                    if (errorMsg) errorMsg.remove();
                }
            });

            // File upload
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            // Drag and drop
            const uploadArea = document.querySelector('.file-upload-area');
            if (uploadArea) {
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('drop', handleDrop);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('dragenter', handleDragOver);
            }
        }

        async function loadUserData() {
            try {
                const response = await fetch('/api/users/dashboard/', {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.user_info;
                    dependents = data.dependents;

                    console.log('User data loaded:', currentUser);
                    console.log('Dependents loaded:', dependents);

                    // Update UI with user info if needed
                    updateUserInfo();
                } else {
                    showAlert('Failed to load user data', 'error');
                    throw new Error('Failed to load user data');
                }
            } catch (error) {
                console.error('Error loading user data:', error);
                showAlert('Error loading user data', 'error');
                throw error;
            }
        }

        function updateUserInfo() {
            // You can add user info display here if needed
            console.log(`Loaded user: ${currentUser.full_name} with ${dependents.length} dependents`);
        }

        // Clear form for new request
        function clearFormForNewRequest() {
            console.log('Clearing form for new request');

            // Clear current request
            currentRequest = null;

            // Clear status badge
            updateRequestStatus();

            // Clear pending deletions
            window.itemsToDelete = [];

            // Clear form items
            document.getElementById('employeeNormalItems').innerHTML = '';
            document.getElementById('employeeChronicItems').innerHTML = '';
            document.getElementById('dependentsNormalItems').innerHTML = '';
            document.getElementById('dependentsChronicItems').innerHTML = '';

            // Clear attachments
            document.getElementById('attachmentsList').innerHTML = '';

            // Clear file input
            const fileInput = document.getElementById('fileInput');
            if (fileInput) fileInput.value = '';

            // Clear notes
            document.getElementById('employeeNotes').value = '';

            // Clear validation errors
            clearValidationErrors();

            // Update totals and buttons
            updateTotals();
            updateFormButtons();

            // Clear URL parameters
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('id');
            window.history.replaceState({}, '', newUrl);
        }

        // Update request status display
        function updateRequestStatus() {
            const statusDiv = document.getElementById('requestStatus');
            const statusText = document.getElementById('statusText');
            const statusIcon = document.getElementById('statusIcon');

            if (currentRequest && currentRequest.status) {
                statusDiv.style.display = 'inline-flex';

                // Remove all status classes
                statusDiv.className = 'status-badge';

                // Set status-specific content and styling
                const status = currentRequest.status;
                const statusDisplay = currentRequest.status_display || status;

                statusText.textContent = statusDisplay;

                // Add status-specific class and icon
                switch (status) {
                    case 'draft':
                        statusDiv.classList.add('status-draft');
                        statusIcon.textContent = '📝';
                        break;
                    case 'submitted':
                        statusDiv.classList.add('status-submitted');
                        statusIcon.textContent = '📤';
                        break;
                    case 'approved':
                        statusDiv.classList.add('status-approved');
                        statusIcon.textContent = '✅';
                        break;
                    case 'rejected':
                        statusDiv.classList.add('status-rejected');
                        statusIcon.textContent = '❌';
                        break;
                    case 'paid':
                        statusDiv.classList.add('status-paid');
                        statusIcon.textContent = '💰';
                        break;
                    default:
                        statusDiv.classList.add('status-draft');
                        statusIcon.textContent = '📋';
                }
            } else {
                statusDiv.style.display = 'none';
            }
        }

        // Clear previous validation errors
        function clearValidationErrors() {
            // Remove error classes from all fields
            document.querySelectorAll('.field-error').forEach(field => {
                field.classList.remove('field-error');
            });

            // Remove error messages
            document.querySelectorAll('.error-message').forEach(msg => {
                msg.remove();
            });
        }

        // Add error styling to a field
        function addFieldError(field, message) {
            field.classList.add('field-error');

            // Add error message if provided
            if (message) {
                const errorMsg = document.createElement('span');
                errorMsg.className = 'error-message';
                errorMsg.textContent = message;
                field.parentNode.appendChild(errorMsg);
            }
        }

        // Validate required fields before saving
        function validateRequiredFields() {
            const errors = [];

            // Clear previous errors
            clearValidationErrors();

            // Check request month
            const monthInput = document.getElementById('requestMonth');
            if (!monthInput.value) {
                errors.push('Request Month is required');
                addFieldError(monthInput, 'Please select a month');
            }

            // Check each item card for required fields
            const itemCards = document.querySelectorAll('.item-card');
            itemCards.forEach((card, index) => {
                const itemNumber = index + 1;

                // Skip items marked for deletion
                if (card.classList.contains('item-marked-for-deletion')) {
                    return;
                }

                // Check category
                const categoryField = card.querySelector('.item-category');
                if (!categoryField?.value) {
                    errors.push(`Item ${itemNumber}: Category is required`);
                    addFieldError(categoryField, 'Select a category');
                }

                // Check dependent (only for dependent items)
                const dependentSelect = card.querySelector('.item-dependent');
                if (dependentSelect && !dependentSelect.value) {
                    errors.push(`Item ${itemNumber}: Dependent selection is required`);
                    addFieldError(dependentSelect, 'Select a dependent');
                }

                // Check description
                const descriptionField = card.querySelector('.item-description');
                const description = descriptionField?.value;
                if (!description || description.trim() === '') {
                    errors.push(`Item ${itemNumber}: Description is required`);
                    addFieldError(descriptionField, 'Enter a description');
                }

                // Check cost
                const costField = card.querySelector('.item-cost');
                const cost = costField?.value;
                if (!cost || parseFloat(cost) <= 0) {
                    errors.push(`Item ${itemNumber}: Valid cost is required`);
                    addFieldError(costField, 'Enter a valid cost');
                }

                // Check date of service
                const dateField = card.querySelector('.item-date');
                if (!dateField?.value) {
                    errors.push(`Item ${itemNumber}: Date of service is required`);
                    addFieldError(dateField, 'Select a date');
                }
            });

            return errors;
        }

        // Collect all medical expense items from the form
        function collectFormItems() {
            const items = [];
            const itemCards = document.querySelectorAll('.item-card');

            itemCards.forEach(card => {
                const category = card.querySelector('.item-category')?.value;
                const description = card.querySelector('.item-description')?.value;
                const cost = card.querySelector('.item-cost')?.value;
                const dateOfService = card.querySelector('.item-date')?.value;
                const dependentSelect = card.querySelector('.item-dependent');

                // Determine type and beneficiary from the container
                let type = 'normal';
                let beneficiary = 'employee';

                const container = card.closest('[id$="Items"]');
                if (container) {
                    const containerId = container.id;
                    if (containerId.includes('Chronic')) type = 'chronic';
                    if (containerId.includes('dependents')) beneficiary = 'dependent';
                }

                // Only include items with required fields
                if (category && description && cost && dateOfService) {
                    const item = {
                        category: category,
                        type: type,
                        beneficiary: beneficiary,
                        description: description,
                        cost: parseFloat(cost),
                        date_of_service: dateOfService
                    };

                    // Add dependent ID if this is a dependent item
                    if (beneficiary === 'dependent' && dependentSelect?.value) {
                        item.dependent_id = parseInt(dependentSelect.value);
                    }

                    // Add existing item ID if this is an update
                    const itemId = card.getAttribute('data-item-id');
                    if (itemId && !isNaN(itemId)) {
                        item.id = parseInt(itemId);
                    }

                    items.push(item);
                }
            });

            return items;
        }

        // Save request items to the server with proper create/update logic
        async function saveRequestItems(requestId, items) {
            try {
                console.log(`Saving ${items.length} items for request ${requestId}`);

                // First, get existing items to determine what to update vs create
                const existingItemsResponse = await fetch(`/api/requests/${requestId}/`, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                    }
                });

                let existingItems = [];
                if (existingItemsResponse.ok) {
                    const requestData = await existingItemsResponse.json();
                    existingItems = requestData.items || [];
                    console.log('Existing items:', existingItems);
                }

                // Create a map of existing items by their ID
                const existingItemsMap = new Map();
                existingItems.forEach(item => {
                    existingItemsMap.set(item.id, item);
                });

                // Process each item - update existing or create new
                for (const item of items) {
                    if (item.id && existingItemsMap.has(item.id)) {
                        // Update existing item
                        console.log(`Updating existing item ${item.id}:`, item);
                        const response = await fetch(`/api/requests/${requestId}/items/${item.id}/`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Token ${authToken}`,
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(item)
                        });

                        if (!response.ok) {
                            console.error('Failed to update item:', item, response.status);
                            const errorData = await response.json();
                            console.error('Update error details:', errorData);
                        } else {
                            console.log('Item updated successfully:', item);
                        }
                    } else {
                        // Create new item
                        console.log('Creating new item:', item);
                        const response = await fetch(`/api/requests/${requestId}/items/`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Token ${authToken}`,
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(item)
                        });

                        if (!response.ok) {
                            console.error('Failed to create item:', item, response.status);
                            const errorData = await response.json();
                            console.error('Create error details:', errorData);
                        } else {
                            console.log('Item created successfully:', item);
                        }
                    }
                }

                // Handle deletion of items that were removed from the form
                if (window.itemsToDelete && window.itemsToDelete.length > 0) {
                    console.log(`Deleting ${window.itemsToDelete.length} removed items:`, window.itemsToDelete);

                    for (const itemId of window.itemsToDelete) {
                        try {
                            const deleteResponse = await fetch(`/api/requests/${requestId}/items/${itemId}/delete/`, {
                                method: 'DELETE',
                                headers: {
                                    'Authorization': `Token ${authToken}`,
                                }
                            });

                            if (deleteResponse.ok) {
                                console.log(`Item ${itemId} deleted successfully`);
                            } else {
                                console.error(`Failed to delete item ${itemId}:`, deleteResponse.status);
                            }
                        } catch (error) {
                            console.error(`Error deleting item ${itemId}:`, error);
                        }
                    }

                    // Clear the deletion list after processing
                    window.itemsToDelete = [];
                }

                console.log('All items processed successfully');
            } catch (error) {
                console.error('Error saving items:', error);
                showAlert('Some items may not have been saved properly', 'warning');
            }
        }





        function updateTotals() {
            let employeeNormal = 0;
            let employeeChronic = 0;
            let dependentsNormal = 0;
            let dependentsChronic = 0;

            // Calculate totals from all item cards
            document.querySelectorAll('.item-card').forEach(card => {
                const costInput = card.querySelector('.item-cost');
                const cost = parseFloat(costInput.value) || 0;

                const containerId = card.closest('.items-container').id;

                if (containerId === 'employeeNormalItems') {
                    employeeNormal += cost;
                } else if (containerId === 'employeeChronicItems') {
                    employeeChronic += cost;
                } else if (containerId === 'dependentsNormalItems') {
                    dependentsNormal += cost;
                } else if (containerId === 'dependentsChronicItems') {
                    dependentsChronic += cost;
                }
            });

            // Update display
            document.getElementById('employeeNormalTotal').textContent = `${employeeNormal.toFixed(2)} EGP`;
            document.getElementById('employeeChronicTotal').textContent = `${employeeChronic.toFixed(2)} EGP`;
            document.getElementById('dependentsNormalTotal').textContent = `${dependentsNormal.toFixed(2)} EGP`;
            document.getElementById('dependentsChronicTotal').textContent = `${dependentsChronic.toFixed(2)} EGP`;

            const grandTotal = employeeNormal + employeeChronic + dependentsNormal + dependentsChronic;
            document.getElementById('grandTotal').textContent = `${grandTotal.toFixed(2)} EGP`;
        }

        function updateFormButtons() {
            const hasItems = document.querySelectorAll('.item-card').length > 0;
            document.getElementById('submitBtn').disabled = !hasItems;
            document.getElementById('coversheetBtn').disabled = !hasItems || !currentRequest;
        }

        window.saveDraft = async function() {
            const spinner = document.getElementById('draftSpinner');
            const saveButton = document.querySelector('button[onclick*="saveDraft"]');

            if (spinner) spinner.classList.remove('hidden');
            if (saveButton) saveButton.disabled = true;

            try {
                // Validate required fields first
                const validationErrors = validateRequiredFields();
                if (validationErrors.length > 0) {
                    const errorMessage = 'Please fill in all required fields (marked with *):\n\n' +
                                       validationErrors.map(error => '• ' + error).join('\n');
                    showAlert(errorMessage, 'error');
                    return;
                }

                const monthInput = document.getElementById('requestMonth');
                const notesInput = document.getElementById('employeeNotes');

                // Collect all form data including items
                const requestData = {
                    month: monthInput.value,
                    employee_notes: notesInput.value || ''
                };

                // Collect all medical expense items from the form
                const items = collectFormItems();

                console.log('Saving draft with data:', requestData);
                console.log('Items to save:', items);
                console.log('Items with IDs (updates):', items.filter(item => item.id));
                console.log('Items without IDs (creates):', items.filter(item => !item.id));
                console.log('Items to delete:', window.itemsToDelete || []);

                let response;
                const csrfToken = getCSRFToken();

                console.log('Current request state:', currentRequest);

                if (currentRequest && currentRequest.id) {
                    // Update existing request
                    console.log(`Updating existing request: ${currentRequest.id}`);
                    response = await fetch(`/api/requests/${currentRequest.id}/update/`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Token ${authToken}`,
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken,
                        },
                        body: JSON.stringify(requestData)
                    });
                } else {
                    // Create new request
                    console.log('Creating new request');
                    response = await fetch('/api/requests/create/', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Token ${authToken}`,
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken,
                        },
                        body: JSON.stringify(requestData)
                    });
                }

                console.log('Response status:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Response data:', data);

                    if (!currentRequest && data.request) {
                        currentRequest = data.request;
                        // Clear any pending deletions for new request
                        window.itemsToDelete = [];
                        // Update URL to include request ID
                        const newUrl = new URL(window.location);
                        newUrl.searchParams.set('id', currentRequest.id);
                        window.history.replaceState({}, '', newUrl);
                    }

                    // Now save the items if we have any and request is in draft status
                    if (items.length > 0 && currentRequest && currentRequest.id) {
                        // Only save items if request is in draft status
                        if (!currentRequest.status || currentRequest.status === 'draft') {
                            console.log('Saving items for draft request:', currentRequest.id);
                            await saveRequestItems(currentRequest.id, items);
                        } else {
                            console.log('Cannot save items - request is not in draft status:', currentRequest.status);
                            showAlert('Cannot modify items - request is no longer in draft status', 'warning');
                        }
                    }

                    showAlert('Draft saved successfully!', 'success');
                    updateFormButtons();
                    updateRequestStatus();

                    // Upload any pending files
                    if (window.pendingFiles && window.pendingFiles.length > 0) {
                        showAlert(`Uploading ${window.pendingFiles.length} pending files...`, 'success');
                        for (let file of window.pendingFiles) {
                            if (validateFile(file)) {
                                uploadFile(file);
                            }
                        }
                        window.pendingFiles = [];
                    }
                } else {
                    console.error('Response not OK:', response.status, response.statusText);

                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (parseError) {
                        console.error('Failed to parse error response as JSON:', parseError);
                        errorData = {
                            error: `Server error (${response.status}): ${response.statusText}`
                        };
                    }

                    console.error('Save error:', errorData);

                    // Handle case where request already exists for this month
                    if (errorData.existing_request_id) {
                        // This should not happen anymore since we check on month selection
                        // But if it does, just show the error without duplicate prompts
                        showAlert('This request already exists. Please use the month selector to load existing requests.', 'warning');
                        return;
                    }

                    showAlert(errorData.error || 'Failed to save draft', 'error');
                }
            } catch (error) {
                console.error('Save draft error:', error);
                showAlert('Error saving draft: ' + error.message, 'error');
            } finally {
                if (spinner) spinner.classList.add('hidden');
                if (saveButton) saveButton.disabled = false;
            }
        }

        window.submitRequest = async function() {
            if (!currentRequest) {
                showAlert('Please save as draft first', 'warning');
                return;
            }

            // Validate required fields before submitting
            const validationErrors = validateRequiredFields();
            if (validationErrors.length > 0) {
                const errorMessage = 'Cannot submit request. Please fill in all required fields (marked with *):\n\n' +
                                   validationErrors.map(error => '• ' + error).join('\n');
                showAlert(errorMessage, 'error');
                return;
            }

            const spinner = document.getElementById('submitSpinner');
            spinner.classList.remove('hidden');

            try {
                const response = await fetch(`/api/requests/${currentRequest.id}/submit/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    // Update current request status
                    currentRequest.status = 'submitted';
                    currentRequest.status_display = 'Submitted';
                    updateRequestStatus();
                    updateFormButtons();

                    showAlert('Request submitted successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard-test/';
                    }, 2000);
                } else {
                    const errorData = await response.json();
                    showAlert(errorData.error || 'Failed to submit request', 'error');
                }
            } catch (error) {
                showAlert('Error submitting request', 'error');
            } finally {
                spinner.classList.add('hidden');
            }
        }

        window.generateCoversheet = function() {
            if (!currentRequest) {
                showAlert('Please save the request first', 'warning');
                return;
            }

            window.open(`/api/requests/${currentRequest.id}/coversheet/`, '_blank');
        }

        // File handling functions
        function handleFileSelect(event) {
            console.log('handleFileSelect called - files:', event.target.files.length);
            const files = event.target.files;
            handleFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.add('dragover');

            // Show drag status
            const statusDiv = document.getElementById('dragDropStatus');
            if (statusDiv) {
                statusDiv.style.display = 'block';
                statusDiv.textContent = '📁 Drop files here...';
                statusDiv.style.color = '#667eea';
            }
        }

        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('dragover');

            // Update status
            const statusDiv = document.getElementById('dragDropStatus');
            if (statusDiv) {
                statusDiv.textContent = '✓ Files received!';
                statusDiv.style.color = '#28a745';
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }

            const files = event.dataTransfer.files;
            console.log('Files dropped:', files.length);
            handleFiles(files);
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            // Only remove dragover if we're actually leaving the drop zone
            if (!event.currentTarget.contains(event.relatedTarget)) {
                event.currentTarget.classList.remove('dragover');

                // Hide status
                const statusDiv = document.getElementById('dragDropStatus');
                if (statusDiv) {
                    statusDiv.style.display = 'none';
                }
            }
        }

        function handleFiles(files) {
            console.log('Handling files:', files.length);

            if (!currentRequest) {
                // Store files temporarily and show message
                showAlert('Files selected. Please save the request as draft first, then the files will be uploaded automatically.', 'warning');

                // Store files for later upload
                window.pendingFiles = Array.from(files);
                return;
            }

            for (let file of files) {
                if (validateFile(file)) {
                    uploadFile(file);
                }
            }
        }

        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png',
                                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

            if (file.size > maxSize) {
                showAlert(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showAlert(`File ${file.name} has unsupported format.`, 'error');
                return false;
            }

            return true;
        }

        async function uploadFile(file) {
            if (!currentRequest) {
                showAlert('Please save the request first before uploading files', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('attachment_type', 'other'); // Default type
            formData.append('description', file.name);

            try {
                const response = await fetch(`/api/requests/${currentRequest.id}/attachments/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                    },
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();
                    addAttachmentToList(data.attachment);
                    showAlert(`File ${file.name} uploaded successfully!`, 'success');
                } else {
                    const errorData = await response.json();
                    showAlert(errorData.error || `Failed to upload ${file.name}`, 'error');
                }
            } catch (error) {
                showAlert(`Error uploading ${file.name}`, 'error');
            }
        }

        function addAttachmentToList(attachment) {
            console.log('addAttachmentToList called with:', attachment);
            const attachmentsList = document.getElementById('attachmentsList');
            const attachmentHtml = `
                <div class="attachment-item" data-attachment-id="${attachment.id}">
                    <div class="attachment-info">
                        <div class="attachment-icon">📎</div>
                        <div>
                            <div style="font-weight: 600;">${attachment.file_name}</div>
                            <div style="font-size: 12px; color: #6c757d;">${formatFileSize(attachment.file_size)}</div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeAttachment(${attachment.id})">
                        Remove
                    </button>
                </div>
            `;
            attachmentsList.insertAdjacentHTML('beforeend', attachmentHtml);
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        async function removeAttachment(attachmentId) {
            try {
                const response = await fetch(`/api/requests/${currentRequest.id}/attachments/${attachmentId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                    }
                });

                if (response.ok) {
                    document.querySelector(`[data-attachment-id="${attachmentId}"]`).remove();
                    showAlert('Attachment removed successfully!', 'success');
                } else {
                    showAlert('Failed to remove attachment', 'error');
                }
            } catch (error) {
                showAlert('Error removing attachment', 'error');
            }
        }

        function showAlert(message, type) {
            // Only show floating notification (removed top notification)
            showFloatingNotification(message, type);
        }

        function showFloatingNotification(message, type) {
            const container = document.getElementById('floatingNotifications');

            // Limit number of notifications (remove oldest if too many)
            const existingNotifications = container.querySelectorAll('.floating-alert');
            if (existingNotifications.length >= 3) {
                removeFloatingNotification(existingNotifications[0]);
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `floating-alert ${type}`;

            // Add icon based on type
            const icon = type === 'success' ? '✓' : type === 'error' ? '⚠' : 'ℹ';

            notification.innerHTML = `
                <span style="margin-right: 8px; font-weight: bold;">${icon}</span>
                ${message}
                <button class="close-btn" onclick="removeFloatingNotification(this.parentElement)">×</button>
            `;

            // Add to container
            container.appendChild(notification);

            // Trigger show animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-remove after delay
            const autoRemoveDelay = type === 'error' ? 8000 : 5000; // Errors stay longer
            setTimeout(() => {
                removeFloatingNotification(notification);
            }, autoRemoveDelay);
        }

        function removeFloatingNotification(notification) {
            if (notification && notification.parentElement) {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.parentElement.removeChild(notification);
                    }
                }, 300);
            }
        }
    </script>
</body>
</html>