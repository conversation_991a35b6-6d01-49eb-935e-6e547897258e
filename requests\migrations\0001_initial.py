# Generated by Django 5.2.4 on 2025-07-04 16:26

import django.core.validators
import requests.models
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('request_number', models.CharField(blank=True, max_length=20, unique=True)),
                ('month', models.DateField(help_text='Month for which the request is being made')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('under_review', 'Under Review'), ('returned', 'Returned for Correction'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid')], default='draft', max_length=20)),
                ('employee_normal_total', models.Decimal<PERSON>ield(decimal_places=2, default=0.0, max_digits=10)),
                ('employee_chronic_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('dependents_normal_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('dependents_chronic_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('employee_notes', models.TextField(blank=True, help_text="Employee's additional notes")),
                ('reviewer_comments', models.TextField(blank=True, help_text="Medical assistant's comments")),
                ('approver_comments', models.TextField(blank=True, help_text='Medical Poss comments')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Medical Request',
                'verbose_name_plural': 'Medical Requests',
                'db_table': 'medical_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RequestAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=requests.models.request_file_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'])])),
                ('attachment_type', models.CharField(choices=[('prescription', 'Prescription'), ('doctor_report', 'Doctor Report'), ('invoice', 'Invoice'), ('receipt', 'Receipt'), ('dl_request', 'D&L Request'), ('other', 'Other')], max_length=20)),
                ('description', models.CharField(blank=True, max_length=255)),
                ('file_size', models.PositiveIntegerField(help_text='File size in bytes')),
                ('is_verified', models.BooleanField(default=False)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Request Attachment',
                'verbose_name_plural': 'Request Attachments',
                'db_table': 'request_attachments',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='RequestAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('submitted', 'Submitted'), ('reviewed', 'Reviewed'), ('returned', 'Returned'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid'), ('item_added', 'Item Added'), ('item_updated', 'Item Updated'), ('item_deleted', 'Item Deleted'), ('attachment_added', 'Attachment Added'), ('attachment_deleted', 'Attachment Deleted')], max_length=20)),
                ('description', models.TextField()),
                ('old_values', models.JSONField(blank=True, help_text='Previous values before change', null=True)),
                ('new_values', models.JSONField(blank=True, help_text='New values after change', null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Request Audit Log',
                'verbose_name_plural': 'Request Audit Logs',
                'db_table': 'request_audit_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RequestDeadline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.DateField(unique=True)),
                ('submission_deadline', models.DateTimeField()),
                ('review_deadline', models.DateTimeField()),
                ('approval_deadline', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Request Deadline',
                'verbose_name_plural': 'Request Deadlines',
                'db_table': 'request_deadlines',
                'ordering': ['-month'],
            },
        ),
        migrations.CreateModel(
            name='RequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('operation', 'Operation'), ('glasses', 'Glasses'), ('dental', 'Dental'), ('medicine', 'Medicine'), ('dl_services', 'D&L Services'), ('other', 'Other')], max_length=20)),
                ('type', models.CharField(choices=[('normal', 'Normal'), ('chronic', 'Chronic')], max_length=10)),
                ('beneficiary', models.CharField(choices=[('employee', 'Employee'), ('dependent', 'Dependent')], max_length=10)),
                ('description', models.TextField()),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('date_of_service', models.DateField()),
                ('is_flagged', models.BooleanField(default=False)),
                ('flag_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Request Item',
                'verbose_name_plural': 'Request Items',
                'db_table': 'request_items',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'System Configuration',
                'verbose_name_plural': 'System Configurations',
                'db_table': 'system_configurations',
            },
        ),
    ]
