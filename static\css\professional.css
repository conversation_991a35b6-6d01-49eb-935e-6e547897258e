/* Professional Medical Compensation System Styles */

/* Layout Spacing */
.professional-card + .professional-card {
    margin-top: 30px;
}

.stats-grid + .professional-card {
    margin-top: 30px;
}

/* Avatar Styles */
.user-avatar-container {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e0e0e0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.user-avatar:hover {
    border-color: #007bff;
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.25);
}

.user-avatar-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.avatar-initials {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    border: 3px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.avatar-initials-large {
    width: 100px;
    height: 100px;
    font-size: 32px;
    border: 4px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* Professional Header */
.professional-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.header-subtitle {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    margin: 0;
    font-size: 16px;
}

.user-role {
    font-size: 12px;
    opacity: 0.8;
    margin: 0;
}

.user-position {
    font-size: 11px;
    opacity: 0.7;
    margin: 2px 0 0 0;
    font-style: italic;
    color: rgba(255,255,255,0.9);
}

/* Enhanced Cards */
.professional-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.professional-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.card-header-professional {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.card-header-professional h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.card-body-professional {
    padding: 20px;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-submitted {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Professional Buttons */
.btn-professional {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-professional:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-professional-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 10px 22px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-professional-outline:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card-professional {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.stat-card-professional:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-icon-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-icon-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin: 5px 0 0 0;
    font-weight: 500;
}

/* Professional Tables */
.table-professional {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.table-professional th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
}

.table-professional td {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.table-professional tr:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .user-avatar-large {
        width: 60px;
        height: 60px;
    }
    
    .avatar-initials-large {
        width: 60px;
        height: 60px;
        font-size: 18px;
    }
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Floating Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 350px;
}

.notification {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    border-left: 4px solid #667eea;
    padding: 15px 20px;
    margin-bottom: 10px;
    transform: translateX(400px);
    transition: all 0.3s ease;
    opacity: 0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: #56ab2f;
}

.notification.warning {
    border-left-color: #f093fb;
}

.notification.error {
    border-left-color: #f5576c;
}

.notification-title {
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.notification-message {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #666;
}

/* Statistics Count Badges */
.stat-count-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 24px;
    margin: 10px 0;
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-count-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.stat-count-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-count-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-count-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
